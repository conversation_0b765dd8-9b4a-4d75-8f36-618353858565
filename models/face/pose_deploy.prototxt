input: "image"
input_dim: 1
input_dim: 3
input_dim: 1 # Original: 368
input_dim: 1 # Original: 368
# input: "weights"
# input_dim: 1
# input_dim: 71
# input_dim: 184
# input_dim: 184
# input: "labels"
# input_dim: 1
# input_dim: 71
# input_dim: 184
# input_dim: 184

layer {
  name: "conv1_1"
  type: "Convolution"
  bottom: "image"
  top: "conv1_1"
  param {
    lr_mult: 1
    decay_mult: 1
  }
  param {
    lr_mult: 2
    decay_mult: 0
  }
  convolution_param {
    num_output: 64
    pad: 1
    kernel_size: 3
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "conv1_1_re"
  type: "ReLU"
  bottom: "conv1_1"
  top: "conv1_1"
}
layer {
  name: "conv1_2"
  type: "Convolution"
  bottom: "conv1_1"
  top: "conv1_2"
  param {
    lr_mult: 1
    decay_mult: 1
  }
  param {
    lr_mult: 2
    decay_mult: 0
  }
  convolution_param {
    num_output: 64
    pad: 1
    kernel_size: 3
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "conv1_2_re"
  type: "ReLU"
  bottom: "conv1_2"
  top: "conv1_2"
}
layer {
  name: "pool1"
  type: "Pooling"
  bottom: "conv1_2"
  top: "pool1"
  pooling_param {
    pool: MAX
    kernel_size: 2
    stride: 2
  }
}
layer {
  name: "conv2_1"
  type: "Convolution"
  bottom: "pool1"
  top: "conv2_1"
  param {
    lr_mult: 1
    decay_mult: 1
  }
  param {
    lr_mult: 2
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 1
    kernel_size: 3
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "conv2_1_re"
  type: "ReLU"
  bottom: "conv2_1"
  top: "conv2_1"
}
layer {
  name: "conv2_2"
  type: "Convolution"
  bottom: "conv2_1"
  top: "conv2_2"
  param {
    lr_mult: 1
    decay_mult: 1
  }
  param {
    lr_mult: 2
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 1
    kernel_size: 3
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "conv2_2_re"
  type: "ReLU"
  bottom: "conv2_2"
  top: "conv2_2"
}
layer {
  name: "pool2"
  type: "Pooling"
  bottom: "conv2_2"
  top: "pool2"
  pooling_param {
    pool: MAX
    kernel_size: 2
    stride: 2
  }
}
layer {
  name: "conv3_1"
  type: "Convolution"
  bottom: "pool2"
  top: "conv3_1"
  param {
    lr_mult: 1
    decay_mult: 1
  }
  param {
    lr_mult: 2
    decay_mult: 0
  }
  convolution_param {
    num_output: 256
    pad: 1
    kernel_size: 3
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "conv3_1_re"
  type: "ReLU"
  bottom: "conv3_1"
  top: "conv3_1"
}
layer {
  name: "conv3_2"
  type: "Convolution"
  bottom: "conv3_1"
  top: "conv3_2"
  param {
    lr_mult: 1
    decay_mult: 1
  }
  param {
    lr_mult: 2
    decay_mult: 0
  }
  convolution_param {
    num_output: 256
    pad: 1
    kernel_size: 3
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "conv3_2_re"
  type: "ReLU"
  bottom: "conv3_2"
  top: "conv3_2"
}
layer {
  name: "conv3_3"
  type: "Convolution"
  bottom: "conv3_2"
  top: "conv3_3"
  param {
    lr_mult: 1
    decay_mult: 1
  }
  param {
    lr_mult: 2
    decay_mult: 0
  }
  convolution_param {
    num_output: 256
    pad: 1
    kernel_size: 3
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "conv3_3_re"
  type: "ReLU"
  bottom: "conv3_3"
  top: "conv3_3"
}
layer {
  name: "conv3_4"
  type: "Convolution"
  bottom: "conv3_3"
  top: "conv3_4"
  param {
    lr_mult: 1
    decay_mult: 1
  }
  param {
    lr_mult: 2
    decay_mult: 0
  }
  convolution_param {
    num_output: 256
    pad: 1
    kernel_size: 3
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "conv3_4_re"
  type: "ReLU"
  bottom: "conv3_4"
  top: "conv3_4"
}
layer {
  name: "pool3"
  type: "Pooling"
  bottom: "conv3_4"
  top: "pool3"
  pooling_param {
    pool: MAX
    kernel_size: 2
    stride: 2
  }
}
layer {
  name: "conv4_1"
  type: "Convolution"
  bottom: "pool3"
  top: "conv4_1"
  param {
    lr_mult: 1
    decay_mult: 1
  }
  param {
    lr_mult: 2
    decay_mult: 0
  }
  convolution_param {
    num_output: 512
    pad: 1
    kernel_size: 3
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "conv4_1_re"
  type: "ReLU"
  bottom: "conv4_1"
  top: "conv4_1"
}
layer {
  name: "conv4_2"
  type: "Convolution"
  bottom: "conv4_1"
  top: "conv4_2"
  param {
    lr_mult: 1
    decay_mult: 1
  }
  param {
    lr_mult: 2
    decay_mult: 0
  }
  convolution_param {
    num_output: 512
    pad: 1
    kernel_size: 3
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "conv4_2_re"
  type: "ReLU"
  bottom: "conv4_2"
  top: "conv4_2"
}
layer {
  name: "conv4_3"
  type: "Convolution"
  bottom: "conv4_2"
  top: "conv4_3"
  param {
    lr_mult: 1
    decay_mult: 1
  }
  param {
    lr_mult: 2
    decay_mult: 0
  }
  convolution_param {
    num_output: 512
    pad: 1
    kernel_size: 3
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "conv4_3_re"
  type: "ReLU"
  bottom: "conv4_3"
  top: "conv4_3"
}
layer {
  name: "conv4_4"
  type: "Convolution"
  bottom: "conv4_3"
  top: "conv4_4"
  param {
    lr_mult: 1
    decay_mult: 1
  }
  param {
    lr_mult: 2
    decay_mult: 0
  }
  convolution_param {
    num_output: 512
    pad: 1
    kernel_size: 3
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "conv4_4_re"
  type: "ReLU"
  bottom: "conv4_4"
  top: "conv4_4"
}
layer {
  name: "conv5_1"
  type: "Convolution"
  bottom: "conv4_4"
  top: "conv5_1"
  param {
    lr_mult: 1
    decay_mult: 1
  }
  param {
    lr_mult: 2
    decay_mult: 0
  }
  convolution_param {
    num_output: 512
    pad: 1
    kernel_size: 3
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "conv5_1_re"
  type: "ReLU"
  bottom: "conv5_1"
  top: "conv5_1"
}
layer {
  name: "conv5_2"
  type: "Convolution"
  bottom: "conv5_1"
  top: "conv5_2"
  param {
    lr_mult: 1
    decay_mult: 1
  }
  param {
    lr_mult: 2
    decay_mult: 0
  }
  convolution_param {
    num_output: 512
    pad: 1
    kernel_size: 3
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "conv5_2_re"
  type: "ReLU"
  bottom: "conv5_2"
  top: "conv5_2"
}
layer {
  name: "conv5_3_CPM"
  type: "Convolution"
  bottom: "conv5_2"
  top: "conv5_3_CPM"
  param {
    lr_mult: 1
    decay_mult: 1
  }
  param {
    lr_mult: 2
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 1
    kernel_size: 3
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "conv5_3_CPM_re"
  type: "ReLU"
  bottom: "conv5_3_CPM"
  top: "conv5_3_CPM"
}
layer {
  name: "conv6_1_CPM"
  type: "Convolution"
  bottom: "conv5_3_CPM"
  top: "conv6_1_CPM"
  param {
    lr_mult: 1
    decay_mult: 1
  }
  param {
    lr_mult: 2
    decay_mult: 0
  }
  convolution_param {
    num_output: 512
    pad: 0
    kernel_size: 1
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "conv6_1_CPM_re"
  type: "ReLU"
  bottom: "conv6_1_CPM"
  top: "conv6_1_CPM"
}
layer {
  name: "conv6_2_CPM"
  type: "Convolution"
  bottom: "conv6_1_CPM"
  top: "conv6_2_CPM"
  param {
    lr_mult: 1
    decay_mult: 1
  }
  param {
    lr_mult: 2
    decay_mult: 0
  }
  convolution_param {
    num_output: 71
    pad: 0
    kernel_size: 1
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "features_in_stage_2"
  type: "Concat"
  bottom: "conv6_2_CPM"
  bottom: "conv5_3_CPM"
  top: "features_in_stage_2"
  concat_param {
    axis: 1
  }
}
layer {
  name: "Mconv1_stage2"
  type: "Convolution"
  bottom: "features_in_stage_2"
  top: "Mconv1_stage2"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mconv1_stage2_re"
  type: "ReLU"
  bottom: "Mconv1_stage2"
  top: "Mconv1_stage2"
}
layer {
  name: "Mconv2_stage2"
  type: "Convolution"
  bottom: "Mconv1_stage2"
  top: "Mconv2_stage2"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mconv2_stage2_re"
  type: "ReLU"
  bottom: "Mconv2_stage2"
  top: "Mconv2_stage2"
}
layer {
  name: "Mconv3_stage2"
  type: "Convolution"
  bottom: "Mconv2_stage2"
  top: "Mconv3_stage2"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mconv3_stage2_re"
  type: "ReLU"
  bottom: "Mconv3_stage2"
  top: "Mconv3_stage2"
}
layer {
  name: "Mconv4_stage2"
  type: "Convolution"
  bottom: "Mconv3_stage2"
  top: "Mconv4_stage2"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mconv4_stage2_re"
  type: "ReLU"
  bottom: "Mconv4_stage2"
  top: "Mconv4_stage2"
}
layer {
  name: "Mconv5_stage2"
  type: "Convolution"
  bottom: "Mconv4_stage2"
  top: "Mconv5_stage2"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mconv5_stage2_re"
  type: "ReLU"
  bottom: "Mconv5_stage2"
  top: "Mconv5_stage2"
}
layer {
  name: "Mconv6_stage2"
  type: "Convolution"
  bottom: "Mconv5_stage2"
  top: "Mconv6_stage2"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 0
    kernel_size: 1
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mconv6_stage2_re"
  type: "ReLU"
  bottom: "Mconv6_stage2"
  top: "Mconv6_stage2"
}
layer {
  name: "Mconv7_stage2"
  type: "Convolution"
  bottom: "Mconv6_stage2"
  top: "Mconv7_stage2"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 71
    pad: 0
    kernel_size: 1
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "features_in_stage_3"
  type: "Concat"
  bottom: "Mconv7_stage2"
  bottom: "conv5_3_CPM"
  top: "features_in_stage_3"
  concat_param {
    axis: 1
  }
}
layer {
  name: "Mconv1_stage3"
  type: "Convolution"
  bottom: "features_in_stage_3"
  top: "Mconv1_stage3"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mconv1_stage3_re"
  type: "ReLU"
  bottom: "Mconv1_stage3"
  top: "Mconv1_stage3"
}
layer {
  name: "Mconv2_stage3"
  type: "Convolution"
  bottom: "Mconv1_stage3"
  top: "Mconv2_stage3"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mconv2_stage3_re"
  type: "ReLU"
  bottom: "Mconv2_stage3"
  top: "Mconv2_stage3"
}
layer {
  name: "Mconv3_stage3"
  type: "Convolution"
  bottom: "Mconv2_stage3"
  top: "Mconv3_stage3"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mconv3_stage3_re"
  type: "ReLU"
  bottom: "Mconv3_stage3"
  top: "Mconv3_stage3"
}
layer {
  name: "Mconv4_stage3"
  type: "Convolution"
  bottom: "Mconv3_stage3"
  top: "Mconv4_stage3"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mconv4_stage3_re"
  type: "ReLU"
  bottom: "Mconv4_stage3"
  top: "Mconv4_stage3"
}
layer {
  name: "Mconv5_stage3"
  type: "Convolution"
  bottom: "Mconv4_stage3"
  top: "Mconv5_stage3"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mconv5_stage3_re"
  type: "ReLU"
  bottom: "Mconv5_stage3"
  top: "Mconv5_stage3"
}
layer {
  name: "Mconv6_stage3"
  type: "Convolution"
  bottom: "Mconv5_stage3"
  top: "Mconv6_stage3"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 0
    kernel_size: 1
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mconv6_stage3_re"
  type: "ReLU"
  bottom: "Mconv6_stage3"
  top: "Mconv6_stage3"
}
layer {
  name: "Mconv7_stage3"
  type: "Convolution"
  bottom: "Mconv6_stage3"
  top: "Mconv7_stage3"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 71
    pad: 0
    kernel_size: 1
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "features_in_stage_4"
  type: "Concat"
  bottom: "Mconv7_stage3"
  bottom: "conv5_3_CPM"
  top: "features_in_stage_4"
  concat_param {
    axis: 1
  }
}
layer {
  name: "Mconv1_stage4"
  type: "Convolution"
  bottom: "features_in_stage_4"
  top: "Mconv1_stage4"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mconv1_stage4_re"
  type: "ReLU"
  bottom: "Mconv1_stage4"
  top: "Mconv1_stage4"
}
layer {
  name: "Mconv2_stage4"
  type: "Convolution"
  bottom: "Mconv1_stage4"
  top: "Mconv2_stage4"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mconv2_stage4_re"
  type: "ReLU"
  bottom: "Mconv2_stage4"
  top: "Mconv2_stage4"
}
layer {
  name: "Mconv3_stage4"
  type: "Convolution"
  bottom: "Mconv2_stage4"
  top: "Mconv3_stage4"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mconv3_stage4_re"
  type: "ReLU"
  bottom: "Mconv3_stage4"
  top: "Mconv3_stage4"
}
layer {
  name: "Mconv4_stage4"
  type: "Convolution"
  bottom: "Mconv3_stage4"
  top: "Mconv4_stage4"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mconv4_stage4_re"
  type: "ReLU"
  bottom: "Mconv4_stage4"
  top: "Mconv4_stage4"
}
layer {
  name: "Mconv5_stage4"
  type: "Convolution"
  bottom: "Mconv4_stage4"
  top: "Mconv5_stage4"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mconv5_stage4_re"
  type: "ReLU"
  bottom: "Mconv5_stage4"
  top: "Mconv5_stage4"
}
layer {
  name: "Mconv6_stage4"
  type: "Convolution"
  bottom: "Mconv5_stage4"
  top: "Mconv6_stage4"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 0
    kernel_size: 1
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mconv6_stage4_re"
  type: "ReLU"
  bottom: "Mconv6_stage4"
  top: "Mconv6_stage4"
}
layer {
  name: "Mconv7_stage4"
  type: "Convolution"
  bottom: "Mconv6_stage4"
  top: "Mconv7_stage4"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 71
    pad: 0
    kernel_size: 1
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "features_in_stage_5"
  type: "Concat"
  bottom: "Mconv7_stage4"
  bottom: "conv5_3_CPM"
  top: "features_in_stage_5"
  concat_param {
    axis: 1
  }
}
layer {
  name: "Mconv1_stage5"
  type: "Convolution"
  bottom: "features_in_stage_5"
  top: "Mconv1_stage5"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mconv1_stage5_re"
  type: "ReLU"
  bottom: "Mconv1_stage5"
  top: "Mconv1_stage5"
}
layer {
  name: "Mconv2_stage5"
  type: "Convolution"
  bottom: "Mconv1_stage5"
  top: "Mconv2_stage5"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mconv2_stage5_re"
  type: "ReLU"
  bottom: "Mconv2_stage5"
  top: "Mconv2_stage5"
}
layer {
  name: "Mconv3_stage5"
  type: "Convolution"
  bottom: "Mconv2_stage5"
  top: "Mconv3_stage5"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mconv3_stage5_re"
  type: "ReLU"
  bottom: "Mconv3_stage5"
  top: "Mconv3_stage5"
}
layer {
  name: "Mconv4_stage5"
  type: "Convolution"
  bottom: "Mconv3_stage5"
  top: "Mconv4_stage5"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mconv4_stage5_re"
  type: "ReLU"
  bottom: "Mconv4_stage5"
  top: "Mconv4_stage5"
}
layer {
  name: "Mconv5_stage5"
  type: "Convolution"
  bottom: "Mconv4_stage5"
  top: "Mconv5_stage5"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mconv5_stage5_re"
  type: "ReLU"
  bottom: "Mconv5_stage5"
  top: "Mconv5_stage5"
}
layer {
  name: "Mconv6_stage5"
  type: "Convolution"
  bottom: "Mconv5_stage5"
  top: "Mconv6_stage5"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 0
    kernel_size: 1
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mconv6_stage5_re"
  type: "ReLU"
  bottom: "Mconv6_stage5"
  top: "Mconv6_stage5"
}
layer {
  name: "Mconv7_stage5"
  type: "Convolution"
  bottom: "Mconv6_stage5"
  top: "Mconv7_stage5"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 71
    pad: 0
    kernel_size: 1
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "features_in_stage_6"
  type: "Concat"
  bottom: "Mconv7_stage5"
  bottom: "conv5_3_CPM"
  top: "features_in_stage_6"
  concat_param {
    axis: 1
  }
}
layer {
  name: "Mconv1_stage6"
  type: "Convolution"
  bottom: "features_in_stage_6"
  top: "Mconv1_stage6"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mconv1_stage6_re"
  type: "ReLU"
  bottom: "Mconv1_stage6"
  top: "Mconv1_stage6"
}
layer {
  name: "Mconv2_stage6"
  type: "Convolution"
  bottom: "Mconv1_stage6"
  top: "Mconv2_stage6"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mconv2_stage6_re"
  type: "ReLU"
  bottom: "Mconv2_stage6"
  top: "Mconv2_stage6"
}
layer {
  name: "Mconv3_stage6"
  type: "Convolution"
  bottom: "Mconv2_stage6"
  top: "Mconv3_stage6"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mconv3_stage6_re"
  type: "ReLU"
  bottom: "Mconv3_stage6"
  top: "Mconv3_stage6"
}
layer {
  name: "Mconv4_stage6"
  type: "Convolution"
  bottom: "Mconv3_stage6"
  top: "Mconv4_stage6"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mconv4_stage6_re"
  type: "ReLU"
  bottom: "Mconv4_stage6"
  top: "Mconv4_stage6"
}
layer {
  name: "Mconv5_stage6"
  type: "Convolution"
  bottom: "Mconv4_stage6"
  top: "Mconv5_stage6"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mconv5_stage6_re"
  type: "ReLU"
  bottom: "Mconv5_stage6"
  top: "Mconv5_stage6"
}
layer {
  name: "Mconv6_stage6"
  type: "Convolution"
  bottom: "Mconv5_stage6"
  top: "Mconv6_stage6"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 0
    kernel_size: 1
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mconv6_stage6_re"
  type: "ReLU"
  bottom: "Mconv6_stage6"
  top: "Mconv6_stage6"
}
layer {
  name: "Mconv7_stage6"
  type: "Convolution"
  bottom: "Mconv6_stage6"
#   top: "Mconv7_stage6"
  top: "net_output"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 71
    pad: 0
    kernel_size: 1
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
