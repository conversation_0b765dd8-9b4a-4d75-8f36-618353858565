# OpenPose Python Implementation

이 프로젝트는 OpenPose를 순수 파이썬으로 구현한 버전입니다.

## 특징

- **2D 실시간 다중 인물 키포인트 검출**
  - 25개 신체/발 키포인트 추정 (6개 발 키포인트 포함)
  - 2x21개 손 키포인트 추정
  - 70개 얼굴 키포인트 추정

- **입력**: 이미지, 비디오, 웹캠 지원
- **출력**: 키포인트 좌표, 시각화된 이미지
- **하드웨어**: CPU 및 GPU 지원 (OpenCV DNN 사용)

## 설치

```bash
pip install -r requirements.txt
```

## 사용법

### 기본 사용법

```python
import openpose

# OpenPose 초기화
op_wrapper = openpose.WrapperPython()
op_wrapper.configure({
    "model_folder": "./models/",
    "face": True,
    "hand": True
})
op_wrapper.start()

# 이미지 처리
import cv2
datum = openpose.Datum()
image = cv2.imread("image.jpg")
datum.cvInputData = image
op_wrapper.emplaceAndPop([datum])

# 결과 출력
print("Body keypoints:", datum.poseKeypoints)
print("Face keypoints:", datum.faceKeypoints)
print("Hand keypoints:", datum.handKeypoints)
```

### 비디오 처리

```python
import cv2
import openpose

op_wrapper = openpose.WrapperPython()
op_wrapper.configure({"model_folder": "./models/"})
op_wrapper.start()

cap = cv2.VideoCapture("video.mp4")
while True:
    ret, frame = cap.read()
    if not ret:
        break
    
    datum = openpose.Datum()
    datum.cvInputData = frame
    op_wrapper.emplaceAndPop([datum])
    
    cv2.imshow("OpenPose", datum.cvOutputData)
    if cv2.waitKey(1) & 0xFF == ord('q'):
        break

cap.release()
cv2.destroyAllWindows()
```

## 모듈 구조

- `openpose.core`: 기본 데이터 구조 (Array, Point, Rectangle 등)
- `openpose.pose`: 포즈 추정 기능
- `openpose.face`: 얼굴 키포인트 검출
- `openpose.hand`: 손 키포인트 검출
- `openpose.net`: 신경망 처리
- `openpose.utilities`: 유틸리티 함수
- `openpose.wrapper`: 통합 인터페이스

## 모델 파일

모델 파일들은 `models/` 디렉토리에 위치해야 합니다:

```
models/
├── pose/
│   ├── body_25/
│   │   ├── pose_deploy.prototxt
│   │   └── pose_iter_584000.caffemodel
│   └── coco/
│       ├── pose_deploy_linevec.prototxt
│       └── pose_iter_440000.caffemodel
├── face/
│   ├── pose_deploy.prototxt
│   └── pose_iter_116000.caffemodel
└── hand/
    ├── pose_deploy.prototxt
    └── pose_iter_102000.caffemodel
```

## 라이센스

이 구현은 원본 OpenPose 프로젝트의 라이센스를 따릅니다.

## 참고

- 원본 OpenPose: https://github.com/CMU-Perceptual-Computing-Lab/openpose
- 논문: "Realtime Multi-Person 2D Pose Estimation using Part Affinity Fields"
