#!/usr/bin/env python3
"""
OpenPose Python Example: Whole Body (Body + Face + Hands) from Image

This example demonstrates how to extract body, face, and hand keypoints from a single image.
"""

import sys
import cv2
import argparse
import numpy as np
from pathlib import Path

# Add the parent directory to the path to import openpose
sys.path.append(str(Path(__file__).parent.parent))

import openpose


def main():
    # Parse arguments
    parser = argparse.ArgumentParser(description='OpenPose Whole Body from Image')
    parser.add_argument('--image_path', type=str, default='../media/COCO_val2014_000000000192.jpg',
                       help='Path to input image')
    parser.add_argument('--model_folder', type=str, default='../models/',
                       help='Path to model folder')
    parser.add_argument('--net_resolution', type=str, default='368x368',
                       help='Network input resolution')
    parser.add_argument('--face_net_resolution', type=str, default='368x368',
                       help='Face network input resolution')
    parser.add_argument('--hand_net_resolution', type=str, default='368x368',
                       help='Hand network input resolution')
    parser.add_argument('--output_path', type=str, default='',
                       help='Path to save output image')
    parser.add_argument('--display', action='store_true',
                       help='Display output image')
    
    args = parser.parse_args()
    
    try:
        # Check if image exists
        if not Path(args.image_path).exists():
            print(f"Error: Image file not found: {args.image_path}")
            return -1
        
        # Configure OpenPose parameters
        params = {
            "model_folder": args.model_folder,
            "net_resolution": args.net_resolution,
            "pose_model": "BODY_25",
            "alpha_pose": 0.6,
            "render_threshold": 0.05,
            "number_people_max": -1,
            
            # Enable face detection
            "face": True,
            "face_net_resolution": args.face_net_resolution,
            
            # Enable hand detection
            "hand": True,
            "hand_net_resolution": args.hand_net_resolution,
            
            "display": args.display
        }
        
        # Initialize OpenPose
        print("Initializing OpenPose with whole body detection...")
        op_wrapper = openpose.WrapperPython()
        op_wrapper.configure(params)
        op_wrapper.start()
        
        # Load and process image
        print(f"Loading image: {args.image_path}")
        image = cv2.imread(args.image_path)
        
        if image is None:
            print(f"Error: Could not load image: {args.image_path}")
            return -1
        
        # Create datum
        datum = openpose.Datum()
        datum.cvInputData = image
        
        # Process image
        print("Processing image...")
        success = op_wrapper.emplaceAndPop([datum])
        
        if not success:
            print("Error: Failed to process image")
            return -1
        
        # Print body keypoints
        if datum.poseKeypoints is not None and not datum.poseKeypoints.empty():
            print(f"Body keypoints shape: {datum.poseKeypoints.shape}")
            print(f"Number of people detected: {len(datum.poseKeypoints.data)}")
        else:
            print("No body keypoints detected")
        
        # Print face keypoints
        if datum.faceKeypoints is not None and not datum.faceKeypoints.empty():
            print(f"Face keypoints shape: {datum.faceKeypoints.shape}")
            print(f"Number of faces detected: {len(datum.faceKeypoints.data)}")
        else:
            print("No face keypoints detected")
        
        # Print hand keypoints
        if datum.handKeypoints[0] is not None and not datum.handKeypoints[0].empty():
            print(f"Left hand keypoints shape: {datum.handKeypoints[0].shape}")
        else:
            print("No left hand keypoints detected")
        
        if datum.handKeypoints[1] is not None and not datum.handKeypoints[1].empty():
            print(f"Right hand keypoints shape: {datum.handKeypoints[1].shape}")
        else:
            print("No right hand keypoints detected")
        
        # Print detailed keypoints for first person
        if (datum.poseKeypoints is not None and not datum.poseKeypoints.empty() and 
            len(datum.poseKeypoints.data) > 0):
            
            print("\nDetailed keypoints for first person:")
            
            # Body keypoints
            body_keypoints = datum.poseKeypoints.data[0]
            body_parts = [
                "Nose", "Neck", "RShoulder", "RElbow", "RWrist", "LShoulder", "LElbow", "LWrist",
                "MidHip", "RHip", "RKnee", "RAnkle", "LHip", "LKnee", "LAnkle", "REye", "LEye",
                "REar", "LEar", "LBigToe", "LSmallToe", "LHeel", "RBigToe", "RSmallToe", "RHeel"
            ]
            
            print("Body parts:")
            valid_body_parts = 0
            for i, part_name in enumerate(body_parts):
                if i < len(body_keypoints):
                    x, y, conf = body_keypoints[i]
                    if conf > 0.1:
                        print(f"  {part_name}: ({x:.1f}, {y:.1f}) conf: {conf:.3f}")
                        valid_body_parts += 1
            print(f"  Total valid body parts: {valid_body_parts}")
            
            # Face keypoints summary
            if datum.faceKeypoints is not None and not datum.faceKeypoints.empty():
                face_keypoints = datum.faceKeypoints.data[0]
                valid_face_points = sum(1 for kpt in face_keypoints if kpt[2] > 0.1)
                print(f"Face keypoints: {valid_face_points} valid points out of {len(face_keypoints)}")
            
            # Hand keypoints summary
            for hand_idx, hand_name in enumerate(["Left", "Right"]):
                if (datum.handKeypoints[hand_idx] is not None and 
                    not datum.handKeypoints[hand_idx].empty()):
                    hand_keypoints = datum.handKeypoints[hand_idx].data[0]
                    valid_hand_points = sum(1 for kpt in hand_keypoints if kpt[2] > 0.1)
                    print(f"{hand_name} hand keypoints: {valid_hand_points} valid points out of {len(hand_keypoints)}")
        
        # Save or display output
        if datum.cvOutputData is not None:
            if args.output_path:
                print(f"Saving output to: {args.output_path}")
                cv2.imwrite(args.output_path, datum.cvOutputData)
            
            if args.display:
                print("Displaying output (press any key to close)")
                cv2.imshow("OpenPose - Whole Body from Image", datum.cvOutputData)
                cv2.waitKey(0)
                cv2.destroyAllWindows()
        
        print("Processing completed successfully!")
        return 0
        
    except Exception as e:
        print(f"Error: {str(e)}")
        return -1


if __name__ == "__main__":
    sys.exit(main())
