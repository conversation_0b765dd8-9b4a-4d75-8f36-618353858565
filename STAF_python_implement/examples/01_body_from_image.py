#!/usr/bin/env python3
"""
OpenPose Python Example: Body Pose from Image

This example demonstrates how to extract body pose keypoints from a single image.
"""

import sys
import cv2
import argparse
import numpy as np
from pathlib import Path

# Add the parent directory to the path to import openpose
sys.path.append(str(Path(__file__).parent.parent))

import openpose


def main():
    # Parse arguments
    parser = argparse.ArgumentParser(description='OpenPose Body Pose from Image')
    parser.add_argument('--image_path', type=str, default='../media/COCO_val2014_000000000192.jpg',
                       help='Path to input image')
    parser.add_argument('--model_folder', type=str, default='../models/',
                       help='Path to model folder')
    parser.add_argument('--net_resolution', type=str, default='368x368',
                       help='Network input resolution (e.g., 368x368)')
    parser.add_argument('--output_path', type=str, default='',
                       help='Path to save output image')
    parser.add_argument('--display', action='store_true',
                       help='Display output image')
    
    args = parser.parse_args()
    
    try:
        # Check if image exists
        if not Path(args.image_path).exists():
            print(f"Error: Image file not found: {args.image_path}")
            return -1
        
        # Configure OpenPose parameters
        params = {
            "model_folder": args.model_folder,
            "net_resolution": args.net_resolution,
            "pose_model": "BODY_25",
            "alpha_pose": 0.6,
            "render_threshold": 0.05,
            "number_people_max": -1,
            "display": args.display
        }
        
        # Initialize OpenPose
        print("Initializing OpenPose...")
        op_wrapper = openpose.WrapperPython()
        op_wrapper.configure(params)
        op_wrapper.start()
        
        # Load and process image
        print(f"Loading image: {args.image_path}")
        image = cv2.imread(args.image_path)
        
        if image is None:
            print(f"Error: Could not load image: {args.image_path}")
            return -1
        
        # Create datum
        datum = openpose.Datum()
        datum.cvInputData = image
        
        # Process image
        print("Processing image...")
        success = op_wrapper.emplaceAndPop([datum])
        
        if not success:
            print("Error: Failed to process image")
            return -1
        
        # Print results
        if datum.poseKeypoints is not None and not datum.poseKeypoints.empty():
            print(f"Body keypoints shape: {datum.poseKeypoints.shape}")
            print(f"Number of people detected: {len(datum.poseKeypoints.data)}")
            
            # Print keypoints for first person
            if len(datum.poseKeypoints.data) > 0:
                print("\nKeypoints for first person:")
                keypoints = datum.poseKeypoints.data[0]
                body_parts = [
                    "Nose", "Neck", "RShoulder", "RElbow", "RWrist", "LShoulder", "LElbow", "LWrist",
                    "MidHip", "RHip", "RKnee", "RAnkle", "LHip", "LKnee", "LAnkle", "REye", "LEye",
                    "REar", "LEar", "LBigToe", "LSmallToe", "LHeel", "RBigToe", "RSmallToe", "RHeel"
                ]
                
                for i, part_name in enumerate(body_parts):
                    if i < len(keypoints):
                        x, y, conf = keypoints[i]
                        if conf > 0.1:  # Only show confident detections
                            print(f"  {part_name}: ({x:.1f}, {y:.1f}) confidence: {conf:.3f}")
        else:
            print("No pose keypoints detected")
        
        # Save or display output
        if datum.cvOutputData is not None:
            if args.output_path:
                print(f"Saving output to: {args.output_path}")
                cv2.imwrite(args.output_path, datum.cvOutputData)
            
            if args.display:
                print("Displaying output (press any key to close)")
                cv2.imshow("OpenPose - Body Pose from Image", datum.cvOutputData)
                cv2.waitKey(0)
                cv2.destroyAllWindows()
        
        print("Processing completed successfully!")
        return 0
        
    except Exception as e:
        print(f"Error: {str(e)}")
        return -1


if __name__ == "__main__":
    sys.exit(main())
