#!/usr/bin/env python3
"""
OpenPose Python Example: Video Processing

This example demonstrates how to process a video file or webcam stream.
"""

import sys
import cv2
import argparse
import numpy as np
from pathlib import Path
import time

# Add the parent directory to the path to import openpose
sys.path.append(str(Path(__file__).parent.parent))

import openpose


def main():
    # Parse arguments
    parser = argparse.ArgumentParser(description='OpenPose Video Processing')
    parser.add_argument('--input', type=str, default='0',
                       help='Input video file path or webcam index (0 for default webcam)')
    parser.add_argument('--output', type=str, default='',
                       help='Output video file path')
    parser.add_argument('--model_folder', type=str, default='../models/',
                       help='Path to model folder')
    parser.add_argument('--net_resolution', type=str, default='368x368',
                       help='Network input resolution')
    parser.add_argument('--face', action='store_true',
                       help='Enable face detection')
    parser.add_argument('--hand', action='store_true',
                       help='Enable hand detection')
    parser.add_argument('--fps_max', type=float, default=-1.0,
                       help='Maximum FPS for processing (-1 for no limit)')
    parser.add_argument('--display', action='store_true', default=True,
                       help='Display output video')
    parser.add_argument('--write_json', type=str, default='',
                       help='Directory to save JSON keypoint files')
    
    args = parser.parse_args()
    
    try:
        # Configure OpenPose parameters
        params = {
            "model_folder": args.model_folder,
            "net_resolution": args.net_resolution,
            "pose_model": "BODY_25",
            "alpha_pose": 0.6,
            "render_threshold": 0.05,
            "number_people_max": -1,
            "face": args.face,
            "hand": args.hand,
            "display": args.display,
            "write_json": args.write_json
        }
        
        # Initialize OpenPose
        print("Initializing OpenPose...")
        op_wrapper = openpose.WrapperPython()
        op_wrapper.configure(params)
        op_wrapper.start()
        
        # Initialize video capture
        if args.input.isdigit():
            # Webcam
            cap = cv2.VideoCapture(int(args.input))
            print(f"Using webcam {args.input}")
        else:
            # Video file
            if not Path(args.input).exists():
                print(f"Error: Video file not found: {args.input}")
                return -1
            cap = cv2.VideoCapture(args.input)
            print(f"Processing video: {args.input}")
        
        if not cap.isOpened():
            print("Error: Could not open video source")
            return -1
        
        # Get video properties
        fps = cap.get(cv2.CAP_PROP_FPS)
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        print(f"Video properties: {width}x{height} @ {fps:.2f} FPS")
        if total_frames > 0:
            print(f"Total frames: {total_frames}")
        
        # Initialize video writer if output path is specified
        video_writer = None
        if args.output:
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            video_writer = cv2.VideoWriter(args.output, fourcc, fps, (width, height))
            print(f"Output will be saved to: {args.output}")
        
        # Processing loop
        frame_count = 0
        start_time = time.time()
        
        print("Starting video processing (press 'q' to quit)...")
        
        while True:
            ret, frame = cap.read()
            if not ret:
                print("End of video or failed to read frame")
                break
            
            frame_count += 1
            
            # Create datum
            datum = openpose.Datum()
            datum.cvInputData = frame
            datum.frameNumber = frame_count
            
            # Process frame
            success = op_wrapper.emplaceAndPop([datum])
            
            if success and datum.cvOutputData is not None:
                output_frame = datum.cvOutputData
                
                # Add frame info overlay
                info_text = f"Frame: {frame_count}"
                if total_frames > 0:
                    info_text += f"/{total_frames}"
                
                # Add FPS info
                elapsed_time = time.time() - start_time
                if elapsed_time > 0:
                    current_fps = frame_count / elapsed_time
                    info_text += f" | FPS: {current_fps:.1f}"
                
                # Add detection info
                if datum.poseKeypoints is not None and not datum.poseKeypoints.empty():
                    num_people = len(datum.poseKeypoints.data)
                    info_text += f" | People: {num_people}"
                
                cv2.putText(output_frame, info_text, (10, 30), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                
                # Save frame to output video
                if video_writer is not None:
                    video_writer.write(output_frame)
                
                # Display frame
                if args.display:
                    cv2.imshow("OpenPose - Video Processing", output_frame)
                    
                    # Check for quit key
                    key = cv2.waitKey(1) & 0xFF
                    if key == ord('q'):
                        print("Quit requested by user")
                        break
            else:
                print(f"Failed to process frame {frame_count}")
            
            # FPS limiting
            if args.fps_max > 0:
                expected_time = frame_count / args.fps_max
                elapsed_time = time.time() - start_time
                if elapsed_time < expected_time:
                    time.sleep(expected_time - elapsed_time)
            
            # Progress update for video files
            if total_frames > 0 and frame_count % 30 == 0:
                progress = (frame_count / total_frames) * 100
                print(f"Progress: {progress:.1f}% ({frame_count}/{total_frames})")
        
        # Cleanup
        cap.release()
        if video_writer is not None:
            video_writer.release()
        cv2.destroyAllWindows()
        
        # Final statistics
        total_time = time.time() - start_time
        avg_fps = frame_count / total_time if total_time > 0 else 0
        
        print(f"\nProcessing completed!")
        print(f"Total frames processed: {frame_count}")
        print(f"Total time: {total_time:.2f} seconds")
        print(f"Average FPS: {avg_fps:.2f}")
        
        return 0
        
    except Exception as e:
        print(f"Error: {str(e)}")
        return -1
    
    finally:
        # Ensure cleanup
        try:
            cap.release()
            if video_writer is not None:
                video_writer.release()
            cv2.destroyAllWindows()
        except:
            pass


if __name__ == "__main__":
    sys.exit(main())
