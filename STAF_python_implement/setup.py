from setuptools import setup, find_packages

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

with open("requirements.txt", "r", encoding="utf-8") as fh:
    requirements = [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="openpose-python",
    version="1.7.0",
    author="OpenPose Python Implementation",
    author_email="",
    description="Pure Python implementation of OpenPose for real-time multi-person keypoint detection",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/CMU-Perceptual-Computing-Lab/openpose",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "Intended Audience :: Science/Research",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.7",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Scientific/Engineering :: Image Recognition",
        "Topic :: Software Development :: Libraries :: Python Modules",
    ],
    python_requires=">=3.7",
    install_requires=requirements,
    keywords="openpose, pose estimation, computer vision, keypoint detection, human pose",
    project_urls={
        "Bug Reports": "https://github.com/CMU-Perceptual-Computing-Lab/openpose/issues",
        "Source": "https://github.com/CMU-Perceptual-Computing-Lab/openpose",
        "Documentation": "https://github.com/CMU-Perceptual-Computing-Lab/openpose/tree/master/doc",
    },
)
