"""
OpenPose Wrapper Structures

This module contains configuration structures for OpenPose wrapper.
"""

from typing import Optional, Dict, Any, Tuple
from ..core.enums import *


class WrapperStructPose:
    """Configuration structure for pose estimation."""
    
    def __init__(self,
                 pose_mode: PoseModel = PoseModel.BODY_25,
                 net_input_size: Tuple[int, int] = (368, 368),
                 output_size: Tuple[int, int] = (-1, -1),
                 keypoint_scale_mode: ScaleMode = ScaleMode.InputResolution,
                 num_gpu: int = -1,
                 num_gpu_start: int = 0,
                 scale_number: int = 1,
                 scale_gap: float = 0.25,
                 render_mode: RenderMode = RenderMode.Auto,
                 pose_model: PoseModel = PoseModel.BODY_25,
                 blend_original_frame: bool = True,
                 alpha_pose: float = 0.6,
                 alpha_heatmap: float = 0.7,
                 part_to_show: int = -1,
                 model_folder: str = "./models/",
                 heatmap_types: List[HeatMapType] = None,
                 heatmap_scale_mode: ScaleMode = ScaleMode.ZeroToOne,
                 part_candidates: bool = False,
                 render_threshold: float = 0.05,
                 number_people_max: int = -1,
                 maximize_positives: bool = False,
                 fps_max: float = -1.0,
                 prototxt_path: str = "",
                 caffemodel_path: str = "",
                 upsampling_ratio: float = 0.0,
                 enable_google_logging: bool = True):
        
        self.pose_mode = pose_mode
        self.net_input_size = net_input_size
        self.output_size = output_size
        self.keypoint_scale_mode = keypoint_scale_mode
        self.num_gpu = num_gpu
        self.num_gpu_start = num_gpu_start
        self.scale_number = scale_number
        self.scale_gap = scale_gap
        self.render_mode = render_mode
        self.pose_model = pose_model
        self.blend_original_frame = blend_original_frame
        self.alpha_pose = alpha_pose
        self.alpha_heatmap = alpha_heatmap
        self.part_to_show = part_to_show
        self.model_folder = model_folder
        self.heatmap_types = heatmap_types or []
        self.heatmap_scale_mode = heatmap_scale_mode
        self.part_candidates = part_candidates
        self.render_threshold = render_threshold
        self.number_people_max = number_people_max
        self.maximize_positives = maximize_positives
        self.fps_max = fps_max
        self.prototxt_path = prototxt_path
        self.caffemodel_path = caffemodel_path
        self.upsampling_ratio = upsampling_ratio
        self.enable_google_logging = enable_google_logging


class WrapperStructFace:
    """Configuration structure for face detection."""
    
    def __init__(self,
                 enable: bool = False,
                 detector: DetectionMode = DetectionMode.Body,
                 net_input_size: Tuple[int, int] = (368, 368),
                 render_mode: RenderMode = RenderMode.Auto,
                 alpha_pose: float = 0.6,
                 alpha_heatmap: float = 0.7,
                 render_threshold: float = 0.4):
        
        self.enable = enable
        self.detector = detector
        self.net_input_size = net_input_size
        self.render_mode = render_mode
        self.alpha_pose = alpha_pose
        self.alpha_heatmap = alpha_heatmap
        self.render_threshold = render_threshold


class WrapperStructHand:
    """Configuration structure for hand detection."""
    
    def __init__(self,
                 enable: bool = False,
                 detector: DetectionMode = DetectionMode.Body,
                 net_input_size: Tuple[int, int] = (368, 368),
                 scale_number: int = 1,
                 scale_range: float = 0.4,
                 render_mode: RenderMode = RenderMode.Auto,
                 alpha_pose: float = 0.6,
                 alpha_heatmap: float = 0.7,
                 render_threshold: float = 0.2):
        
        self.enable = enable
        self.detector = detector
        self.net_input_size = net_input_size
        self.scale_number = scale_number
        self.scale_range = scale_range
        self.render_mode = render_mode
        self.alpha_pose = alpha_pose
        self.alpha_heatmap = alpha_heatmap
        self.render_threshold = render_threshold


class WrapperStructExtra:
    """Configuration structure for extra functionality."""
    
    def __init__(self,
                 reconstruct_3d: bool = False,
                 min_views_3d: int = 2,
                 identification: bool = False,
                 tracking: int = -1,
                 ik_threads: int = 0):
        
        self.reconstruct_3d = reconstruct_3d
        self.min_views_3d = min_views_3d
        self.identification = identification
        self.tracking = tracking
        self.ik_threads = ik_threads


class WrapperStructInput:
    """Configuration structure for input."""
    
    def __init__(self,
                 producer_type: str = "None",
                 producer_string: str = "",
                 frame_first: int = 0,
                 frame_step: int = 1,
                 frame_last: int = -1,
                 real_time_processing: bool = False,
                 frame_flip: bool = False,
                 frame_rotate: int = 0,
                 frames_repeat: bool = False,
                 camera_resolution: Tuple[int, int] = (-1, -1),
                 webcam_resolution: Tuple[int, int] = (-1, -1),
                 camera_fps: float = 30.0):
        
        self.producer_type = producer_type
        self.producer_string = producer_string
        self.frame_first = frame_first
        self.frame_step = frame_step
        self.frame_last = frame_last
        self.real_time_processing = real_time_processing
        self.frame_flip = frame_flip
        self.frame_rotate = frame_rotate
        self.frames_repeat = frames_repeat
        self.camera_resolution = camera_resolution
        self.webcam_resolution = webcam_resolution
        self.camera_fps = camera_fps


class WrapperStructOutput:
    """Configuration structure for output."""
    
    def __init__(self,
                 verbose: float = -1.0,
                 write_keypoint: str = "",
                 write_keypoint_format: DataFormat = DataFormat.Json,
                 write_json: str = "",
                 write_coco_json: str = "",
                 write_coco_foot_json: str = "",
                 write_images: str = "",
                 write_images_format: str = "png",
                 write_video: str = "",
                 write_video_fps: float = -1.0,
                 write_video_with_audio: bool = False,
                 write_heatmaps: str = "",
                 write_heatmaps_format: str = "png",
                 write_video_3d: str = "",
                 write_video_adam: str = "",
                 write_bvh: str = "",
                 udp_host: str = "",
                 udp_port: str = "8051"):
        
        self.verbose = verbose
        self.write_keypoint = write_keypoint
        self.write_keypoint_format = write_keypoint_format
        self.write_json = write_json
        self.write_coco_json = write_coco_json
        self.write_coco_foot_json = write_coco_foot_json
        self.write_images = write_images
        self.write_images_format = write_images_format
        self.write_video = write_video
        self.write_video_fps = write_video_fps
        self.write_video_with_audio = write_video_with_audio
        self.write_heatmaps = write_heatmaps
        self.write_heatmaps_format = write_heatmaps_format
        self.write_video_3d = write_video_3d
        self.write_video_adam = write_video_adam
        self.write_bvh = write_bvh
        self.udp_host = udp_host
        self.udp_port = udp_port


class WrapperStructGui:
    """Configuration structure for GUI."""
    
    def __init__(self,
                 display_mode: int = -1,
                 gui_verbose: bool = False,
                 full_screen: bool = False):
        
        self.display_mode = display_mode
        self.gui_verbose = gui_verbose
        self.full_screen = full_screen
