"""
OpenPose Python Wrapper

Main wrapper class for OpenPose functionality in Python.
"""

import cv2
import numpy as np
from typing import Dict, Any, List, Optional, Union
import threading
import time

from ..core.datum import Datum, VectorDatum
from ..core.array import Array
from ..core.point import Point
from ..core.enums import ThreadManagerMode, PoseModel, RenderMode
from ..pose.pose_extractor import Po<PERSON>Extractor
from ..pose.pose_renderer import Po<PERSON><PERSON><PERSON><PERSON>
from ..face.face_extractor import FaceExtractor
from ..face.face_renderer import FaceRenderer
from ..hand.hand_extractor import HandExtractor
from ..hand.hand_renderer import HandRenderer
from ..net.net_processor import NetProcessor
from .wrapper_structs import *


class WrapperPython:
    """
    Main Python wrapper for OpenPose functionality.
    
    This class provides a simplified interface for pose estimation,
    similar to the C++ OpenPose wrapper.
    """
    
    def __init__(self, thread_manager_mode: ThreadManagerMode = ThreadManagerMode.Synchronous):
        """
        Initialize WrapperPython.
        
        Args:
            thread_manager_mode: Threading mode for processing
        """
        self.thread_manager_mode = thread_manager_mode
        
        # Configuration structures
        self.wrapper_struct_pose = WrapperStructPose()
        self.wrapper_struct_face = WrapperStructFace()
        self.wrapper_struct_hand = WrapperStructHand()
        self.wrapper_struct_extra = WrapperStructExtra()
        self.wrapper_struct_input = WrapperStructInput()
        self.wrapper_struct_output = WrapperStructOutput()
        self.wrapper_struct_gui = WrapperStructGui()
        
        # Core components
        self.pose_extractor: Optional[PoseExtractor] = None
        self.pose_renderer: Optional[PoseRenderer] = None
        self.face_extractor: Optional[FaceExtractor] = None
        self.face_renderer: Optional[FaceRenderer] = None
        self.hand_extractor: Optional[HandExtractor] = None
        self.hand_renderer: Optional[HandRenderer] = None
        self.net_processor: Optional[NetProcessor] = None
        
        # State
        self.configured = False
        self.started = False
        self.running = False
        
        # Threading
        self.processing_thread: Optional[threading.Thread] = None
        self.input_queue: List[Datum] = []
        self.output_queue: List[Datum] = []
        self.queue_lock = threading.Lock()
        
    def configure(self, params: Dict[str, Any]):
        """
        Configure OpenPose with given parameters.
        
        Args:
            params: Configuration parameters dictionary
        """
        try:
            # Parse parameters and update configuration structures
            self._parse_parameters(params)
            
            # Initialize components
            self._initialize_components()
            
            self.configured = True
            
        except Exception as e:
            raise RuntimeError(f"Failed to configure OpenPose: {str(e)}")
    
    def _parse_parameters(self, params: Dict[str, Any]):
        """Parse parameters and update configuration structures."""
        # Pose parameters
        if "model_folder" in params:
            self.wrapper_struct_pose.model_folder = params["model_folder"]
        
        if "pose_model" in params:
            if isinstance(params["pose_model"], str):
                pose_model_str = params["pose_model"].upper()
                if pose_model_str == "BODY_25":
                    self.wrapper_struct_pose.pose_model = PoseModel.BODY_25
                elif pose_model_str == "COCO_18":
                    self.wrapper_struct_pose.pose_model = PoseModel.COCO_18
                elif pose_model_str == "MPI_15":
                    self.wrapper_struct_pose.pose_model = PoseModel.MPI_15
            else:
                self.wrapper_struct_pose.pose_model = params["pose_model"]
        
        if "net_resolution" in params:
            if isinstance(params["net_resolution"], str):
                # Parse string format like "368x368"
                parts = params["net_resolution"].split('x')
                if len(parts) == 2:
                    width, height = int(parts[0]), int(parts[1])
                    self.wrapper_struct_pose.net_input_size = (width, height)
            elif isinstance(params["net_resolution"], (tuple, list)):
                self.wrapper_struct_pose.net_input_size = tuple(params["net_resolution"])
        
        if "scale_number" in params:
            self.wrapper_struct_pose.scale_number = params["scale_number"]
        
        if "scale_gap" in params:
            self.wrapper_struct_pose.scale_gap = params["scale_gap"]
        
        if "render_threshold" in params:
            self.wrapper_struct_pose.render_threshold = params["render_threshold"]
        
        if "alpha_pose" in params:
            self.wrapper_struct_pose.alpha_pose = params["alpha_pose"]
        
        if "number_people_max" in params:
            self.wrapper_struct_pose.number_people_max = params["number_people_max"]
        
        # Face parameters
        if "face" in params:
            self.wrapper_struct_face.enable = bool(params["face"])
        
        if "face_net_resolution" in params:
            if isinstance(params["face_net_resolution"], str):
                parts = params["face_net_resolution"].split('x')
                if len(parts) == 2:
                    width, height = int(parts[0]), int(parts[1])
                    self.wrapper_struct_face.net_input_size = (width, height)
        
        # Hand parameters
        if "hand" in params:
            self.wrapper_struct_hand.enable = bool(params["hand"])
        
        if "hand_net_resolution" in params:
            if isinstance(params["hand_net_resolution"], str):
                parts = params["hand_net_resolution"].split('x')
                if len(parts) == 2:
                    width, height = int(parts[0]), int(parts[1])
                    self.wrapper_struct_hand.net_input_size = (width, height)
        
        # Output parameters
        if "write_json" in params:
            self.wrapper_struct_output.write_json = params["write_json"]
        
        if "write_images" in params:
            self.wrapper_struct_output.write_images = params["write_images"]
        
        if "display" in params:
            self.wrapper_struct_gui.display_mode = 2 if params["display"] else -1
    
    def _initialize_components(self):
        """Initialize OpenPose components."""
        # Initialize pose extractor
        self.pose_extractor = PoseExtractor(
            pose_model=self.wrapper_struct_pose.pose_model,
            model_folder=self.wrapper_struct_pose.model_folder,
            scale_number=self.wrapper_struct_pose.scale_number,
            scale_gap=self.wrapper_struct_pose.scale_gap,
            render_threshold=self.wrapper_struct_pose.render_threshold,
            num_people_max=self.wrapper_struct_pose.number_people_max
        )
        
        # Initialize pose renderer
        self.pose_renderer = PoseRenderer(
            pose_model=self.wrapper_struct_pose.pose_model,
            alpha_pose=self.wrapper_struct_pose.alpha_pose,
            render_threshold=self.wrapper_struct_pose.render_threshold
        )
        
        # Initialize network processor
        self.net_processor = NetProcessor(
            net_input_size=self.wrapper_struct_pose.net_input_size,
            scale_number=self.wrapper_struct_pose.scale_number,
            scale_gap=self.wrapper_struct_pose.scale_gap
        )
    
    def start(self):
        """Start OpenPose processing."""
        if not self.configured:
            raise RuntimeError("OpenPose must be configured before starting")
        
        if self.started:
            return
        
        try:
            # Initialize components on thread
            if self.pose_extractor:
                self.pose_extractor.initialize_on_thread()
            
            self.started = True
            self.running = True
            
            # Start processing thread for asynchronous modes
            if self.thread_manager_mode != ThreadManagerMode.Synchronous:
                self.processing_thread = threading.Thread(target=self._processing_loop)
                self.processing_thread.daemon = True
                self.processing_thread.start()
                
        except Exception as e:
            raise RuntimeError(f"Failed to start OpenPose: {str(e)}")
    
    def stop(self):
        """Stop OpenPose processing."""
        if not self.started:
            return
        
        self.running = False
        
        if self.processing_thread and self.processing_thread.is_alive():
            self.processing_thread.join(timeout=5.0)
        
        self.started = False
    
    def emplaceAndPop(self, datums: Union[List[Datum], VectorDatum]) -> bool:
        """
        Process datums synchronously.
        
        Args:
            datums: Input datums to process
            
        Returns:
            True if processing was successful
        """
        if not self.started:
            raise RuntimeError("OpenPose must be started before processing")
        
        if isinstance(datums, VectorDatum):
            datum_list = list(datums)
        else:
            datum_list = datums
        
        if not datum_list:
            return False
        
        try:
            for datum in datum_list:
                self._process_datum(datum)
            return True
            
        except Exception as e:
            print(f"Error processing datums: {str(e)}")
            return False
    
    def waitAndEmplace(self, datums: Union[List[Datum], VectorDatum]) -> bool:
        """
        Add datums to input queue for asynchronous processing.
        
        Args:
            datums: Input datums to add to queue
            
        Returns:
            True if datums were added successfully
        """
        if not self.started:
            return False
        
        if isinstance(datums, VectorDatum):
            datum_list = list(datums)
        else:
            datum_list = datums
        
        with self.queue_lock:
            self.input_queue.extend(datum_list)
        
        return True
    
    def waitAndPop(self) -> Optional[List[Datum]]:
        """
        Get processed datums from output queue.
        
        Returns:
            List of processed datums or None if queue is empty
        """
        if not self.started:
            return None
        
        with self.queue_lock:
            if self.output_queue:
                return [self.output_queue.pop(0)]
        
        return None
    
    def _processing_loop(self):
        """Main processing loop for asynchronous modes."""
        while self.running:
            # Get datums from input queue
            datums_to_process = []
            with self.queue_lock:
                if self.input_queue:
                    datums_to_process = [self.input_queue.pop(0)]
            
            if datums_to_process:
                # Process datums
                for datum in datums_to_process:
                    try:
                        self._process_datum(datum)
                        
                        # Add to output queue
                        with self.queue_lock:
                            self.output_queue.append(datum)
                            
                    except Exception as e:
                        print(f"Error in processing loop: {str(e)}")
            else:
                # Sleep briefly if no work to do
                time.sleep(0.001)
    
    def _process_datum(self, datum: Datum):
        """
        Process a single datum.
        
        Args:
            datum: Datum to process
        """
        if datum.cvInputData is None:
            return
        
        # Preprocess input
        input_arrays, input_size, scale_ratios = self.net_processor.preprocess_image(datum.cvInputData)
        datum.inputNetData = input_arrays
        
        if not input_arrays:
            return
        
        # Forward pass through pose network
        network_output = self.pose_extractor.forward_pass(input_arrays, input_size, scale_ratios)
        
        # Postprocess output
        heatmaps, pafs = self.net_processor.postprocess_output(network_output, input_size, scale_ratios)
        
        # Extract poses
        if not heatmaps.empty():
            pose_keypoints, pose_scores = self.pose_extractor.extract_poses(heatmaps, pafs)
            datum.poseKeypoints = pose_keypoints
            datum.poseScores = pose_scores
            datum.poseHeatMaps = heatmaps
        
        # Render output
        if datum.poseKeypoints is not None and not datum.poseKeypoints.empty():
            output_image = self.pose_renderer.render_pose_keypoints(
                datum.cvInputData, datum.poseKeypoints, pose_scores=datum.poseScores
            )
            datum.cvOutputData = output_image
        else:
            datum.cvOutputData = datum.cvInputData.copy()
    
    def exec(self):
        """Execute OpenPose in blocking mode (not implemented for Python version)."""
        raise NotImplementedError("exec() method is not implemented in Python version. Use start() and emplaceAndPop() instead.")
    
    def isRunning(self) -> bool:
        """Check if OpenPose is running."""
        return self.running
    
    def __del__(self):
        """Destructor to ensure proper cleanup."""
        self.stop()
