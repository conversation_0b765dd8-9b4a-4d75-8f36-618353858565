"""
OpenPose Non-Maximum Suppression

This module implements non-maximum suppression for peak detection.
"""

import numpy as np
import cv2
from typing import List, Tuple, Optional

from ..core.array import Array


class NonMaximumSuppression:
    """
    Non-Maximum Suppression for peak detection in heatmaps.
    
    This class implements NMS to find local maxima in pose heatmaps.
    """
    
    def __init__(self, 
                 threshold: float = 0.05,
                 window_size: int = 3,
                 max_peaks: int = 50):
        """
        Initialize NonMaximumSuppression.
        
        Args:
            threshold: Minimum confidence threshold for peaks
            window_size: Size of the NMS window
            max_peaks: Maximum number of peaks to return per part
        """
        self.threshold = threshold
        self.window_size = window_size
        self.max_peaks = max_peaks
    
    def find_peaks(self, heatmaps: Array) -> List[List[Tuple[float, float, float]]]:
        """
        Find peaks in heatmaps using non-maximum suppression.
        
        Args:
            heatmaps: Input heatmaps array
            
        Returns:
            List of peaks for each body part [(x, y, confidence), ...]
        """
        if heatmaps.empty():
            return []
        
        heatmap_data = heatmaps.data
        
        # Handle different array shapes
        if len(heatmap_data.shape) == 4:
            # Remove batch dimension
            heatmap_data = heatmap_data[0]
        
        if len(heatmap_data.shape) != 3:
            raise ValueError(f"Expected 3D heatmap data, got shape {heatmap_data.shape}")
        
        num_parts = heatmap_data.shape[0]
        peaks = []
        
        for part_id in range(num_parts):
            heatmap = heatmap_data[part_id]
            part_peaks = self._find_peaks_single_heatmap(heatmap)
            peaks.append(part_peaks)
        
        return peaks
    
    def _find_peaks_single_heatmap(self, heatmap: np.ndarray) -> List[Tuple[float, float, float]]:
        """Find peaks in a single heatmap."""
        # Apply Gaussian smoothing
        smoothed = cv2.GaussianBlur(heatmap, (3, 3), 0, 0)
        
        # Find local maxima
        peaks = []
        h, w = smoothed.shape
        
        # Use a more efficient approach with dilation
        kernel = np.ones((self.window_size, self.window_size), np.uint8)
        dilated = cv2.dilate(smoothed, kernel, iterations=1)
        
        # Find points where original equals dilated (local maxima)
        local_maxima = (smoothed == dilated) & (smoothed > self.threshold)
        
        # Get coordinates of local maxima
        y_coords, x_coords = np.where(local_maxima)
        
        # Create list of peaks with coordinates and confidence
        for x, y in zip(x_coords, y_coords):
            confidence = smoothed[y, x]
            peaks.append((float(x), float(y), float(confidence)))
        
        # Sort by confidence and keep top peaks
        peaks.sort(key=lambda p: p[2], reverse=True)
        peaks = peaks[:self.max_peaks]
        
        return peaks
    
    def refine_peaks(self, peaks: List[Tuple[float, float, float]], 
                    heatmap: np.ndarray) -> List[Tuple[float, float, float]]:
        """
        Refine peak locations using sub-pixel accuracy.
        
        Args:
            peaks: List of initial peaks
            heatmap: Original heatmap
            
        Returns:
            List of refined peaks
        """
        refined_peaks = []
        
        for x, y, conf in peaks:
            # Sub-pixel refinement using quadratic interpolation
            refined_x, refined_y = self._subpixel_refinement(heatmap, int(x), int(y))
            refined_peaks.append((refined_x, refined_y, conf))
        
        return refined_peaks
    
    def _subpixel_refinement(self, heatmap: np.ndarray, x: int, y: int) -> Tuple[float, float]:
        """
        Perform sub-pixel refinement of peak location.
        
        Args:
            heatmap: Input heatmap
            x, y: Integer peak coordinates
            
        Returns:
            Refined (x, y) coordinates
        """
        h, w = heatmap.shape
        
        # Check bounds
        if x <= 0 or x >= w-1 or y <= 0 or y >= h-1:
            return float(x), float(y)
        
        # Get neighboring values
        try:
            # X direction refinement
            dx = 0.0
            if x > 0 and x < w-1:
                left = heatmap[y, x-1]
                center = heatmap[y, x]
                right = heatmap[y, x+1]
                
                # Quadratic interpolation
                if (2*center - left - right) != 0:
                    dx = 0.5 * (left - right) / (2*center - left - right)
                    dx = max(-0.5, min(0.5, dx))  # Clamp to reasonable range
            
            # Y direction refinement
            dy = 0.0
            if y > 0 and y < h-1:
                top = heatmap[y-1, x]
                center = heatmap[y, x]
                bottom = heatmap[y+1, x]
                
                # Quadratic interpolation
                if (2*center - top - bottom) != 0:
                    dy = 0.5 * (top - bottom) / (2*center - top - bottom)
                    dy = max(-0.5, min(0.5, dy))  # Clamp to reasonable range
            
            refined_x = float(x) + dx
            refined_y = float(y) + dy
            
        except (IndexError, ZeroDivisionError):
            # Fallback to original coordinates
            refined_x = float(x)
            refined_y = float(y)
        
        return refined_x, refined_y
    
    def apply_nms_to_peaks(self, peaks: List[Tuple[float, float, float]], 
                          min_distance: float = 5.0) -> List[Tuple[float, float, float]]:
        """
        Apply NMS to remove nearby peaks.
        
        Args:
            peaks: List of peaks
            min_distance: Minimum distance between peaks
            
        Returns:
            Filtered list of peaks
        """
        if not peaks:
            return []
        
        # Sort by confidence
        sorted_peaks = sorted(peaks, key=lambda p: p[2], reverse=True)
        
        filtered_peaks = []
        
        for peak in sorted_peaks:
            x, y, conf = peak
            
            # Check if this peak is too close to any already selected peak
            too_close = False
            for selected_peak in filtered_peaks:
                sx, sy, _ = selected_peak
                distance = np.sqrt((x - sx)**2 + (y - sy)**2)
                
                if distance < min_distance:
                    too_close = True
                    break
            
            if not too_close:
                filtered_peaks.append(peak)
        
        return filtered_peaks
    
    def set_parameters(self, threshold: Optional[float] = None,
                      window_size: Optional[int] = None,
                      max_peaks: Optional[int] = None):
        """Update NMS parameters."""
        if threshold is not None:
            self.threshold = threshold
        if window_size is not None:
            self.window_size = window_size
        if max_peaks is not None:
            self.max_peaks = max_peaks
