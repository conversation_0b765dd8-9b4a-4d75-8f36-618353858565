"""
OpenPose Net Processor

This module handles neural network input/output processing.
"""

import cv2
import numpy as np
from typing import List, Tuple, Optional, Union

from ..core.array import Array
from ..core.point import Point


class NetProcessor:
    """
    Neural network processor for OpenPose.
    
    Handles input preprocessing and output postprocessing for pose estimation networks.
    """
    
    def __init__(self, net_input_size: Tuple[int, int] = (368, 368),
                 scale_number: int = 1,
                 scale_gap: float = 0.25,
                 upsampling_ratio: float = 0.0):
        """
        Initialize NetProcessor.
        
        Args:
            net_input_size: Input size for the network (width, height)
            scale_number: Number of scales to process
            scale_gap: Gap between scales
            upsampling_ratio: Upsampling ratio for output
        """
        self.net_input_size = net_input_size
        self.scale_number = scale_number
        self.scale_gap = scale_gap
        self.upsampling_ratio = upsampling_ratio
        
        # Calculate scales
        self.scales = self._calculate_scales()
    
    def _calculate_scales(self) -> List[float]:
        """Calculate scales for multi-scale processing."""
        scales = []
        
        if self.scale_number == 1:
            scales.append(1.0)
        else:
            for i in range(self.scale_number):
                scale = 1.0 + (i - (self.scale_number - 1) / 2.0) * self.scale_gap
                scales.append(scale)
        
        return scales
    
    def preprocess_image(self, image: np.ndarray) -> Tuple[List[Array], Point, List[float]]:
        """
        Preprocess image for network input.
        
        Args:
            image: Input image (BGR format)
            
        Returns:
            Tuple of (input_arrays, input_size, scale_ratios)
        """
        if image is None or image.size == 0:
            return [], Point(), []
        
        input_arrays = []
        scale_ratios = []
        
        original_height, original_width = image.shape[:2]
        input_size = Point(original_width, original_height)
        
        for scale in self.scales:
            # Calculate scaled dimensions
            scaled_width = int(self.net_input_size[0] * scale)
            scaled_height = int(self.net_input_size[1] * scale)
            
            # Resize image
            resized_image = cv2.resize(image, (scaled_width, scaled_height))
            
            # Convert to blob format
            blob = self._image_to_blob(resized_image)
            
            # Create Array and add to list
            input_array = Array(blob)
            input_arrays.append(input_array)
            
            # Calculate scale ratio
            scale_ratio_x = scaled_width / original_width
            scale_ratio_y = scaled_height / original_height
            scale_ratios.append((scale_ratio_x + scale_ratio_y) / 2.0)
        
        return input_arrays, input_size, scale_ratios
    
    def _image_to_blob(self, image: np.ndarray) -> np.ndarray:
        """
        Convert image to blob format for network input.
        
        Args:
            image: Input image (BGR format)
            
        Returns:
            Blob in format (1, channels, height, width)
        """
        # Convert BGR to RGB
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # Normalize to [0, 1]
        image_normalized = image_rgb.astype(np.float32) / 255.0
        
        # Subtract mean and divide by std (ImageNet normalization)
        mean = np.array([0.485, 0.456, 0.406])
        std = np.array([0.229, 0.224, 0.225])
        
        image_normalized = (image_normalized - mean) / std
        
        # Convert to CHW format
        image_chw = np.transpose(image_normalized, (2, 0, 1))
        
        # Add batch dimension
        blob = np.expand_dims(image_chw, axis=0)
        
        return blob
    
    def postprocess_output(self, network_output: Array, 
                          input_size: Point,
                          scale_ratios: List[float]) -> Tuple[Array, Array]:
        """
        Postprocess network output.
        
        Args:
            network_output: Raw network output
            input_size: Original input size
            scale_ratios: Scale ratios used for preprocessing
            
        Returns:
            Tuple of (heatmaps, pafs)
        """
        if network_output.empty():
            return Array(), Array()
        
        output_data = network_output.data
        
        # Handle different output formats
        if len(output_data.shape) == 4:
            # Format: (batch, channels, height, width)
            output_data = output_data[0]  # Remove batch dimension
        
        # Split into heatmaps and PAFs
        # This depends on the specific model architecture
        # For BODY_25: 26 heatmaps (25 body parts + 1 background) + 52 PAFs (26 connections * 2)
        num_heatmaps = self._get_num_heatmaps()
        
        if output_data.shape[0] >= num_heatmaps:
            heatmaps_data = output_data[:num_heatmaps]
            pafs_data = output_data[num_heatmaps:]
        else:
            # Fallback: assume all are heatmaps
            heatmaps_data = output_data
            pafs_data = np.array([])
        
        # Resize to original input size if needed
        if self.upsampling_ratio > 0:
            target_height = int(input_size.y * self.upsampling_ratio)
            target_width = int(input_size.x * self.upsampling_ratio)
            
            heatmaps_resized = self._resize_output(heatmaps_data, target_width, target_height)
            pafs_resized = self._resize_output(pafs_data, target_width, target_height) if pafs_data.size > 0 else pafs_data
        else:
            heatmaps_resized = heatmaps_data
            pafs_resized = pafs_data
        
        return Array(heatmaps_resized), Array(pafs_resized)
    
    def _get_num_heatmaps(self) -> int:
        """Get number of heatmap channels (model-dependent)."""
        # This should be set based on the pose model
        # For now, use a default value
        return 26  # BODY_25 + background
    
    def _resize_output(self, output_data: np.ndarray, target_width: int, target_height: int) -> np.ndarray:
        """Resize output data to target dimensions."""
        if output_data.size == 0:
            return output_data
        
        resized_data = []
        
        for channel in range(output_data.shape[0]):
            channel_data = output_data[channel]
            resized_channel = cv2.resize(channel_data, (target_width, target_height))
            resized_data.append(resized_channel)
        
        return np.stack(resized_data, axis=0)
    
    def resize_and_merge_outputs(self, outputs: List[Array], 
                                scale_ratios: List[float]) -> Array:
        """
        Resize and merge outputs from multiple scales.
        
        Args:
            outputs: List of outputs from different scales
            scale_ratios: Scale ratios for each output
            
        Returns:
            Merged output array
        """
        if not outputs or len(outputs) == 0:
            return Array()
        
        if len(outputs) == 1:
            return outputs[0]
        
        # Get target size from the first output
        target_shape = outputs[0].shape
        
        merged_data = np.zeros(target_shape, dtype=np.float32)
        
        for i, (output, scale_ratio) in enumerate(zip(outputs, scale_ratios)):
            output_data = output.data
            
            # Resize to target shape if needed
            if output_data.shape != target_shape:
                resized_data = self._resize_output(output_data, target_shape[2], target_shape[1])
            else:
                resized_data = output_data
            
            # Add to merged data (simple averaging)
            merged_data += resized_data / len(outputs)
        
        return Array(merged_data)
    
    def extract_net_input_size(self, image: np.ndarray) -> Point:
        """
        Extract appropriate network input size based on image dimensions.
        
        Args:
            image: Input image
            
        Returns:
            Network input size as Point
        """
        if image is None or image.size == 0:
            return Point(self.net_input_size[0], self.net_input_size[1])
        
        height, width = image.shape[:2]
        aspect_ratio = width / height
        
        # Maintain aspect ratio while fitting to network input size
        if aspect_ratio > 1:
            # Landscape
            net_width = self.net_input_size[0]
            net_height = int(net_width / aspect_ratio)
        else:
            # Portrait
            net_height = self.net_input_size[1]
            net_width = int(net_height * aspect_ratio)
        
        # Ensure dimensions are multiples of 8 (common requirement for CNNs)
        net_width = (net_width // 8) * 8
        net_height = (net_height // 8) * 8
        
        return Point(net_width, net_height)
    
    def set_net_input_size(self, width: int, height: int):
        """Set network input size."""
        self.net_input_size = (width, height)
        
    def set_scale_parameters(self, scale_number: int, scale_gap: float):
        """Set scale parameters and recalculate scales."""
        self.scale_number = scale_number
        self.scale_gap = scale_gap
        self.scales = self._calculate_scales()
