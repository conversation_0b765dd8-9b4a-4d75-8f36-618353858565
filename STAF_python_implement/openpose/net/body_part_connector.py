"""
OpenPose Body Part Connector

This module handles connecting body parts using Part Affinity Fields.
"""

import numpy as np
from typing import List, Tuple, Optional

from ..core.array import Array


class BodyPartConnector:
    """
    Body part connector using Part Affinity Fields (PAFs).
    
    This class implements the connection algorithm to link detected
    body part candidates into complete poses.
    """
    
    def __init__(self, 
                 inter_min_above_threshold: float = 0.95,
                 inter_threshold: float = 0.05,
                 min_subset_cnt: int = 3,
                 min_subset_score: float = 0.4,
                 scale_net_to_output: float = 1.0):
        """
        Initialize BodyPartConnector.
        
        Args:
            inter_min_above_threshold: Minimum threshold for connection
            inter_threshold: Threshold for inter-connection
            min_subset_cnt: Minimum number of parts in a subset
            min_subset_score: Minimum score for a subset
            scale_net_to_output: Scale factor from network to output
        """
        self.inter_min_above_threshold = inter_min_above_threshold
        self.inter_threshold = inter_threshold
        self.min_subset_cnt = min_subset_cnt
        self.min_subset_score = min_subset_score
        self.scale_net_to_output = scale_net_to_output
    
    def connect_body_parts(self, 
                          peaks: List[List[Tuple[float, float, float]]],
                          pafs: Array,
                          part_pairs: List[List[int]]) -> Tuple[List[List[float]], List[float]]:
        """
        Connect body parts to form complete poses.
        
        Args:
            peaks: List of peaks for each body part
            pafs: Part Affinity Fields
            part_pairs: List of part pair connections
            
        Returns:
            Tuple of (poses, scores)
        """
        if pafs.empty() or not peaks:
            return [], []
        
        paf_data = pafs.data
        num_parts = len(peaks)
        
        # Find connections between parts
        connections = self._find_connections(peaks, paf_data, part_pairs)
        
        # Assemble poses from connections
        poses, scores = self._assemble_poses(peaks, connections, num_parts)
        
        return poses, scores
    
    def _find_connections(self, 
                         peaks: List[List[Tuple[float, float, float]]],
                         paf_data: np.ndarray,
                         part_pairs: List[List[int]]) -> List[List[Tuple[int, int, float]]]:
        """Find connections between body parts using PAFs."""
        connections = []
        
        for pair_idx, (part_a, part_b) in enumerate(part_pairs):
            if part_a >= len(peaks) or part_b >= len(peaks):
                connections.append([])
                continue
            
            peaks_a = peaks[part_a]
            peaks_b = peaks[part_b]
            
            if len(peaks_a) == 0 or len(peaks_b) == 0:
                connections.append([])
                continue
            
            # Get PAF for this connection
            if pair_idx * 2 + 1 >= paf_data.shape[0]:
                connections.append([])
                continue
            
            paf_x = paf_data[pair_idx * 2]
            paf_y = paf_data[pair_idx * 2 + 1]
            
            # Calculate connection scores
            pair_connections = []
            
            for i, peak_a in enumerate(peaks_a):
                for j, peak_b in enumerate(peaks_b):
                    score = self._calculate_paf_score(peak_a, peak_b, paf_x, paf_y)
                    
                    if score > self.inter_threshold:
                        pair_connections.append((i, j, score))
            
            # Sort by score and apply non-maximum suppression
            pair_connections.sort(key=lambda x: x[2], reverse=True)
            pair_connections = self._apply_connection_nms(pair_connections)
            
            connections.append(pair_connections)
        
        return connections
    
    def _calculate_paf_score(self, 
                            peak_a: Tuple[float, float, float],
                            peak_b: Tuple[float, float, float],
                            paf_x: np.ndarray,
                            paf_y: np.ndarray) -> float:
        """Calculate PAF score between two peaks."""
        x1, y1, _ = peak_a
        x2, y2, _ = peak_b
        
        # Calculate direction vector
        dx = x2 - x1
        dy = y2 - y1
        norm = np.sqrt(dx*dx + dy*dy)
        
        if norm == 0:
            return 0.0
        
        # Normalize direction
        dx /= norm
        dy /= norm
        
        # Sample points along the line
        num_samples = max(5, int(norm))
        scores = []
        
        for i in range(num_samples):
            t = i / (num_samples - 1) if num_samples > 1 else 0
            x = int(x1 + t * (x2 - x1))
            y = int(y1 + t * (y2 - y1))
            
            # Check bounds
            if (0 <= x < paf_x.shape[1] and 0 <= y < paf_x.shape[0]):
                # Get PAF values at this point
                paf_val_x = paf_x[y, x]
                paf_val_y = paf_y[y, x]
                
                # Calculate alignment score
                score = dx * paf_val_x + dy * paf_val_y
                scores.append(score)
        
        if not scores:
            return 0.0
        
        # Return average score
        avg_score = np.mean(scores)
        
        # Apply threshold
        valid_scores = [s for s in scores if s > self.inter_min_above_threshold]
        if len(valid_scores) < len(scores) * 0.8:  # At least 80% should be above threshold
            return 0.0
        
        return avg_score
    
    def _apply_connection_nms(self, 
                             connections: List[Tuple[int, int, float]]) -> List[Tuple[int, int, float]]:
        """Apply non-maximum suppression to connections."""
        if not connections:
            return []
        
        # Keep track of used peaks
        used_a = set()
        used_b = set()
        final_connections = []
        
        for i, j, score in connections:
            if i not in used_a and j not in used_b:
                final_connections.append((i, j, score))
                used_a.add(i)
                used_b.add(j)
        
        return final_connections
    
    def _assemble_poses(self, 
                       peaks: List[List[Tuple[float, float, float]]],
                       connections: List[List[Tuple[int, int, float]]],
                       num_parts: int) -> Tuple[List[List[float]], List[float]]:
        """Assemble individual poses from connections."""
        # This is a simplified pose assembly algorithm
        # In practice, this would involve more complex graph algorithms
        
        poses = []
        scores = []
        
        # Create initial subsets from connections
        subsets = []
        
        for pair_idx, pair_connections in enumerate(connections):
            for i, j, score in pair_connections:
                # Try to find existing subset that contains one of these peaks
                found_subset = False
                
                for subset in subsets:
                    if (subset['peaks'][pair_idx * 2] == i or 
                        subset['peaks'][pair_idx * 2 + 1] == j):
                        # Add to existing subset
                        subset['peaks'][pair_idx * 2] = i
                        subset['peaks'][pair_idx * 2 + 1] = j
                        subset['score'] += score
                        subset['count'] += 1
                        found_subset = True
                        break
                
                if not found_subset:
                    # Create new subset
                    new_subset = {
                        'peaks': [-1] * (num_parts * 2),
                        'score': score,
                        'count': 1
                    }
                    new_subset['peaks'][pair_idx * 2] = i
                    new_subset['peaks'][pair_idx * 2 + 1] = j
                    subsets.append(new_subset)
        
        # Convert subsets to poses
        for subset in subsets:
            if subset['count'] >= self.min_subset_cnt:
                avg_score = subset['score'] / subset['count']
                
                if avg_score >= self.min_subset_score:
                    pose = [0.0] * (num_parts * 3)  # x, y, confidence for each part
                    
                    # Fill pose from peaks
                    for part_id in range(num_parts):
                        peak_idx = subset['peaks'][part_id * 2]
                        if peak_idx >= 0 and part_id < len(peaks) and peak_idx < len(peaks[part_id]):
                            x, y, conf = peaks[part_id][peak_idx]
                            pose[part_id * 3] = x * self.scale_net_to_output
                            pose[part_id * 3 + 1] = y * self.scale_net_to_output
                            pose[part_id * 3 + 2] = conf
                    
                    poses.append(pose)
                    scores.append(avg_score)
        
        return poses, scores
