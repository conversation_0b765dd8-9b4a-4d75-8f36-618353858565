"""
OpenPose Hand Renderer

This module contains hand rendering functionality.
"""

import cv2
import numpy as np
from typing import List, Tuple, Optional

from ..core.array import Array


class HandRenderer:
    """Hand renderer for visualizing hand keypoints."""
    
    def __init__(self, alpha_pose: float = 0.6, render_threshold: float = 0.2):
        """
        Initialize HandRenderer.
        
        Args:
            alpha_pose: Alpha blending for hand overlay
            render_threshold: Threshold for rendering keypoints
        """
        self.alpha_pose = alpha_pose
        self.render_threshold = render_threshold
        self.point_radius = 3
        self.line_thickness = 2
        
        # Hand connections (21 keypoints)
        self.hand_connections = [
            # Thumb
            [0, 1], [1, 2], [2, 3], [3, 4],
            # Index finger
            [0, 5], [5, 6], [6, 7], [7, 8],
            # Middle finger
            [0, 9], [9, 10], [10, 11], [11, 12],
            # Ring finger
            [0, 13], [13, 14], [14, 15], [15, 16],
            # Pinky
            [0, 17], [17, 18], [18, 19], [19, 20]
        ]
    
    def render_hand_keypoints(self, frame: np.ndarray, 
                             left_hand_keypoints: Array, 
                             right_hand_keypoints: Array) -> np.ndarray:
        """
        Render hand keypoints on the frame.
        
        Args:
            frame: Input frame
            left_hand_keypoints: Left hand keypoints array
            right_hand_keypoints: Right hand keypoints array
            
        Returns:
            Frame with rendered hand keypoints
        """
        if frame is None:
            return frame
        
        output_frame = frame.copy()
        
        # Render left hand
        if not left_hand_keypoints.empty():
            self._render_single_hand(output_frame, left_hand_keypoints, (0, 255, 0))  # Green for left
        
        # Render right hand
        if not right_hand_keypoints.empty():
            self._render_single_hand(output_frame, right_hand_keypoints, (255, 0, 0))  # Blue for right
        
        return output_frame
    
    def _render_single_hand(self, frame: np.ndarray, hand_keypoints: Array, color: Tuple[int, int, int]):
        """Render keypoints for a single hand."""
        keypoints_data = hand_keypoints.data
        
        # Handle different array shapes
        if len(keypoints_data.shape) == 3:
            # Multiple hands (take first one)
            if keypoints_data.shape[0] > 0:
                keypoints = keypoints_data[0]
            else:
                return
        elif len(keypoints_data.shape) == 2:
            # Single hand
            keypoints = keypoints_data
        else:
            return
        
        # Draw connections first
        for connection in self.hand_connections:
            start_idx, end_idx = connection
            if start_idx < len(keypoints) and end_idx < len(keypoints):
                start_kpt = keypoints[start_idx]
                end_kpt = keypoints[end_idx]
                
                if (len(start_kpt) >= 3 and len(end_kpt) >= 3 and
                    start_kpt[2] > self.render_threshold and end_kpt[2] > self.render_threshold):
                    
                    start_point = (int(start_kpt[0]), int(start_kpt[1]))
                    end_point = (int(end_kpt[0]), int(end_kpt[1]))
                    
                    cv2.line(frame, start_point, end_point, color, self.line_thickness)
        
        # Draw keypoints
        for i, kpt in enumerate(keypoints):
            if len(kpt) >= 3 and kpt[2] > self.render_threshold:
                center = (int(kpt[0]), int(kpt[1]))
                cv2.circle(frame, center, self.point_radius, color, -1)


class HandDetector:
    """Simple hand detector placeholder."""
    
    def __init__(self):
        pass
    
    def detect(self, image: np.ndarray) -> List[Tuple[int, int, int, int]]:
        """Detect hands in image."""
        return []
