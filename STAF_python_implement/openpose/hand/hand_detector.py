"""
OpenPose Hand Detector

This module contains hand detection functionality.
"""

import cv2
import numpy as np
from typing import List, Tuple

from ..core.rectangle import Rectangle


class HandDetector:
    """
    Hand detector using pose-based hand region estimation.
    """
    
    def __init__(self):
        """Initialize hand detector."""
        pass
    
    def detect_hands_from_pose(self, pose_keypoints: np.ndarray) -> Tuple[List[Rectangle], List[Rectangle]]:
        """
        Detect hand regions from pose keypoints.
        
        Args:
            pose_keypoints: Pose keypoints array
            
        Returns:
            Tuple of (left_hand_regions, right_hand_regions)
        """
        left_hand_regions = []
        right_hand_regions = []
        
        if pose_keypoints is None or len(pose_keypoints) == 0:
            return left_hand_regions, right_hand_regions
        
        # For BODY_25 model: LWrist=7, RWrist=4, LElbow=6, RE<PERSON>bow=3
        left_wrist_id = 7
        right_wrist_id = 4
        left_elbow_id = 6
        right_elbow_id = 3
        
        # Estimate left hand region
        if (left_wrist_id < len(pose_keypoints) and left_elbow_id < len(pose_keypoints)):
            wrist_x, wrist_y, wrist_conf = pose_keypoints[left_wrist_id]
            elbow_x, elbow_y, elbow_conf = pose_keypoints[left_elbow_id]
            
            if wrist_conf > 0.1 and elbow_conf > 0.1:
                left_region = self._create_hand_region(wrist_x, wrist_y, elbow_x, elbow_y)
                left_hand_regions.append(left_region)
        
        # Estimate right hand region
        if (right_wrist_id < len(pose_keypoints) and right_elbow_id < len(pose_keypoints)):
            wrist_x, wrist_y, wrist_conf = pose_keypoints[right_wrist_id]
            elbow_x, elbow_y, elbow_conf = pose_keypoints[right_elbow_id]
            
            if wrist_conf > 0.1 and elbow_conf > 0.1:
                right_region = self._create_hand_region(wrist_x, wrist_y, elbow_x, elbow_y)
                right_hand_regions.append(right_region)
        
        return left_hand_regions, right_hand_regions
    
    def _create_hand_region(self, wrist_x: float, wrist_y: float, 
                          elbow_x: float, elbow_y: float) -> Rectangle:
        """Create hand region from wrist and elbow positions."""
        # Calculate hand direction and size
        dx = wrist_x - elbow_x
        dy = wrist_y - elbow_y
        arm_length = np.sqrt(dx*dx + dy*dy)
        
        # Hand size is proportional to arm length
        hand_size = arm_length * 0.8
        
        # Extend from wrist in the direction away from elbow
        if arm_length > 0:
            direction_x = dx / arm_length
            direction_y = dy / arm_length
            
            # Hand center is slightly beyond the wrist
            hand_center_x = wrist_x + direction_x * hand_size * 0.2
            hand_center_y = wrist_y + direction_y * hand_size * 0.2
        else:
            hand_center_x = wrist_x
            hand_center_y = wrist_y
        
        # Create square region around hand center
        hand_x = hand_center_x - hand_size / 2
        hand_y = hand_center_y - hand_size / 2
        
        return Rectangle(hand_x, hand_y, hand_size, hand_size)
