"""
OpenPose Hand Extractor

This module contains hand keypoint extraction functionality.
"""

import cv2
import numpy as np
from typing import List, Tuple, Optional
import os

from ..core.array import Array
from ..core.rectangle import Rectangle
from ..core.enums import DetectionMode


class HandExtractor:
    """
    Hand keypoint extractor.
    
    This class handles hand detection and keypoint extraction.
    """
    
    def __init__(self, 
                 detection_mode: DetectionMode = DetectionMode.Body,
                 net_input_size: Tuple[int, int] = (368, 368),
                 scale_number: int = 1,
                 scale_range: float = 0.4,
                 model_folder: str = "./models/",
                 render_threshold: float = 0.2):
        """
        Initialize HandExtractor.
        
        Args:
            detection_mode: Hand detection mode
            net_input_size: Network input size
            scale_number: Number of scales for hand detection
            scale_range: Scale range for hand detection
            model_folder: Path to model files
            render_threshold: Threshold for rendering keypoints
        """
        self.detection_mode = detection_mode
        self.net_input_size = net_input_size
        self.scale_number = scale_number
        self.scale_range = scale_range
        self.model_folder = model_folder
        self.render_threshold = render_threshold
        
        # Hand model parameters
        self.num_hand_keypoints = 21
        
        # Networks for left and right hands
        self.net = None
        self.initialized = False
        
        # Output
        self.hand_keypoints_left = Array()
        self.hand_keypoints_right = Array()
        self.hand_bounding_boxes_left = Array()
        self.hand_bounding_boxes_right = Array()
    
    def initialize_on_thread(self):
        """Initialize the hand extractor on the current thread."""
        if self.initialized:
            return
        
        try:
            # Load hand keypoint model if available
            self._initialize_hand_keypoint_model()
            
            self.initialized = True
            
        except Exception as e:
            print(f"Warning: Failed to initialize hand extractor: {str(e)}")
            # Continue without hand detection
            self.initialized = True
    
    def _initialize_hand_keypoint_model(self):
        """Initialize hand keypoint extraction model."""
        try:
            # Try to load hand keypoint model
            proto_file = os.path.join(self.model_folder, "hand/pose_deploy.prototxt")
            model_file = os.path.join(self.model_folder, "hand/pose_iter_102000.caffemodel")
            
            if os.path.exists(proto_file) and os.path.exists(model_file):
                self.net = cv2.dnn.readNetFromCaffe(proto_file, model_file)
                
                # Set backend and target
                if cv2.cuda.getCudaEnabledDeviceCount() > 0:
                    self.net.setPreferableBackend(cv2.dnn.DNN_BACKEND_CUDA)
                    self.net.setPreferableTarget(cv2.dnn.DNN_TARGET_CUDA)
                else:
                    self.net.setPreferableBackend(cv2.dnn.DNN_BACKEND_OPENCV)
                    self.net.setPreferableTarget(cv2.dnn.DNN_TARGET_CPU)
            else:
                print(f"Warning: Hand model files not found at {proto_file} or {model_file}")
                self.net = None
                
        except Exception as e:
            print(f"Warning: Could not initialize hand keypoint model: {str(e)}")
            self.net = None
    
    def extract_hands(self, image: np.ndarray, 
                     pose_keypoints: Optional[Array] = None) -> Tuple[List[Array], List[Array]]:
        """
        Extract hand keypoints from image.
        
        Args:
            image: Input image
            pose_keypoints: Body pose keypoints (for hand region detection)
            
        Returns:
            Tuple of ([left_hand_keypoints], [right_hand_keypoints])
        """
        if not self.initialized:
            self.initialize_on_thread()
        
        if image is None or image.size == 0:
            return [Array()], [Array()]
        
        # Detect hand regions
        left_hand_regions, right_hand_regions = self._detect_hand_regions(image, pose_keypoints)
        
        # Extract keypoints for left hands
        left_hand_keypoints = []
        left_hand_boxes = []
        
        for hand_box in left_hand_regions:
            hand_kpts = self._extract_hand_keypoints(image, hand_box)
            if hand_kpts is not None:
                left_hand_keypoints.append(hand_kpts)
                left_hand_boxes.append([hand_box.x, hand_box.y, hand_box.width, hand_box.height])
        
        # Extract keypoints for right hands
        right_hand_keypoints = []
        right_hand_boxes = []
        
        for hand_box in right_hand_regions:
            hand_kpts = self._extract_hand_keypoints(image, hand_box)
            if hand_kpts is not None:
                right_hand_keypoints.append(hand_kpts)
                right_hand_boxes.append([hand_box.x, hand_box.y, hand_box.width, hand_box.height])
        
        # Convert to Arrays
        left_arrays = [Array(kpts) for kpts in left_hand_keypoints] if left_hand_keypoints else [Array()]
        right_arrays = [Array(kpts) for kpts in right_hand_keypoints] if right_hand_keypoints else [Array()]
        
        # Store results
        self.hand_keypoints_left = left_arrays[0] if left_arrays else Array()
        self.hand_keypoints_right = right_arrays[0] if right_arrays else Array()
        
        if left_hand_boxes:
            self.hand_bounding_boxes_left = Array(np.array(left_hand_boxes))
        else:
            self.hand_bounding_boxes_left = Array()
            
        if right_hand_boxes:
            self.hand_bounding_boxes_right = Array(np.array(right_hand_boxes))
        else:
            self.hand_bounding_boxes_right = Array()
        
        return left_arrays, right_arrays
    
    def _detect_hand_regions(self, image: np.ndarray, 
                           pose_keypoints: Optional[Array] = None) -> Tuple[List[Rectangle], List[Rectangle]]:
        """Detect hand regions in the image."""
        left_hand_regions = []
        right_hand_regions = []
        
        if self.detection_mode == DetectionMode.Body and pose_keypoints is not None:
            # Use body keypoints to estimate hand regions
            left_hand_regions, right_hand_regions = self._detect_hands_from_pose(image, pose_keypoints)
        
        return left_hand_regions, right_hand_regions
    
    def _detect_hands_from_pose(self, image: np.ndarray, 
                              pose_keypoints: Array) -> Tuple[List[Rectangle], List[Rectangle]]:
        """Detect hand regions using body pose keypoints."""
        left_hand_regions = []
        right_hand_regions = []
        
        if pose_keypoints.empty():
            return left_hand_regions, right_hand_regions
        
        keypoints_data = pose_keypoints.data
        
        # Handle different array shapes
        if len(keypoints_data.shape) == 3:
            # Multiple people
            for person_id in range(keypoints_data.shape[0]):
                person_keypoints = keypoints_data[person_id]
                left_region, right_region = self._estimate_hand_regions_from_pose(person_keypoints)
                if left_region is not None:
                    left_hand_regions.append(left_region)
                if right_region is not None:
                    right_hand_regions.append(right_region)
        elif len(keypoints_data.shape) == 2:
            # Single person
            left_region, right_region = self._estimate_hand_regions_from_pose(keypoints_data)
            if left_region is not None:
                left_hand_regions.append(left_region)
            if right_region is not None:
                right_hand_regions.append(right_region)
        
        return left_hand_regions, right_hand_regions
    
    def _estimate_hand_regions_from_pose(self, keypoints: np.ndarray) -> Tuple[Optional[Rectangle], Optional[Rectangle]]:
        """Estimate hand regions from pose keypoints."""
        # For BODY_25 model: LWrist=7, RWrist=4, LElbow=6, RElbow=3
        left_wrist_id = 7
        right_wrist_id = 4
        left_elbow_id = 6
        right_elbow_id = 3
        
        left_hand_region = None
        right_hand_region = None
        
        # Estimate left hand region
        if (left_wrist_id < len(keypoints) and left_elbow_id < len(keypoints)):
            wrist_x, wrist_y, wrist_conf = keypoints[left_wrist_id]
            elbow_x, elbow_y, elbow_conf = keypoints[left_elbow_id]
            
            if wrist_conf > 0.1 and elbow_conf > 0.1:
                left_hand_region = self._create_hand_region(wrist_x, wrist_y, elbow_x, elbow_y)
        
        # Estimate right hand region
        if (right_wrist_id < len(keypoints) and right_elbow_id < len(keypoints)):
            wrist_x, wrist_y, wrist_conf = keypoints[right_wrist_id]
            elbow_x, elbow_y, elbow_conf = keypoints[right_elbow_id]
            
            if wrist_conf > 0.1 and elbow_conf > 0.1:
                right_hand_region = self._create_hand_region(wrist_x, wrist_y, elbow_x, elbow_y)
        
        return left_hand_region, right_hand_region
    
    def _create_hand_region(self, wrist_x: float, wrist_y: float, 
                          elbow_x: float, elbow_y: float) -> Rectangle:
        """Create hand region from wrist and elbow positions."""
        # Calculate hand direction and size
        dx = wrist_x - elbow_x
        dy = wrist_y - elbow_y
        arm_length = np.sqrt(dx*dx + dy*dy)
        
        # Hand size is proportional to arm length
        hand_size = arm_length * 0.8
        
        # Extend from wrist in the direction away from elbow
        if arm_length > 0:
            direction_x = dx / arm_length
            direction_y = dy / arm_length
            
            # Hand center is slightly beyond the wrist
            hand_center_x = wrist_x + direction_x * hand_size * 0.2
            hand_center_y = wrist_y + direction_y * hand_size * 0.2
        else:
            hand_center_x = wrist_x
            hand_center_y = wrist_y
        
        # Create square region around hand center
        hand_x = hand_center_x - hand_size / 2
        hand_y = hand_center_y - hand_size / 2
        
        return Rectangle(hand_x, hand_y, hand_size, hand_size)
    
    def _extract_hand_keypoints(self, image: np.ndarray, hand_region: Rectangle) -> Optional[np.ndarray]:
        """Extract hand keypoints from a hand region."""
        if self.net is None:
            # Return dummy keypoints if no model is available
            return self._generate_dummy_hand_keypoints(hand_region)
        
        try:
            # Crop hand region
            x, y, w, h = int(hand_region.x), int(hand_region.y), int(hand_region.width), int(hand_region.height)
            
            # Ensure valid crop region
            x = max(0, x)
            y = max(0, y)
            w = min(w, image.shape[1] - x)
            h = min(h, image.shape[0] - y)
            
            if w <= 0 or h <= 0:
                return None
            
            hand_crop = image[y:y+h, x:x+w]
            
            # Resize to network input size
            hand_resized = cv2.resize(hand_crop, self.net_input_size)
            
            # Create blob
            blob = cv2.dnn.blobFromImage(hand_resized, 1.0/255.0, self.net_input_size, (0, 0, 0), swapRB=False, crop=False)
            
            # Forward pass
            self.net.setInput(blob)
            output = self.net.forward()
            
            # Process output to get keypoints
            hand_keypoints = self._process_hand_output(output, hand_region)
            
            return hand_keypoints
            
        except Exception as e:
            print(f"Error extracting hand keypoints: {str(e)}")
            return self._generate_dummy_hand_keypoints(hand_region)
    
    def _process_hand_output(self, output: np.ndarray, hand_region: Rectangle) -> np.ndarray:
        """Process network output to get hand keypoints."""
        # This is a simplified version
        # In practice, you'd need to implement the specific post-processing for your hand model
        
        return self._generate_dummy_hand_keypoints(hand_region)
    
    def _generate_dummy_hand_keypoints(self, hand_region: Rectangle) -> np.ndarray:
        """Generate dummy hand keypoints when no model is available."""
        keypoints = []
        
        # Generate 21 hand keypoints in a hand-like pattern
        for i in range(self.num_hand_keypoints):
            # Create a rough hand shape
            if i == 0:  # Wrist
                x = hand_region.x + hand_region.width * 0.5
                y = hand_region.y + hand_region.height * 0.8
            elif i <= 4:  # Thumb
                x = hand_region.x + hand_region.width * (0.2 + 0.1 * (i-1))
                y = hand_region.y + hand_region.height * (0.6 - 0.1 * (i-1))
            elif i <= 8:  # Index finger
                x = hand_region.x + hand_region.width * 0.4
                y = hand_region.y + hand_region.height * (0.6 - 0.15 * (i-5))
            elif i <= 12:  # Middle finger
                x = hand_region.x + hand_region.width * 0.5
                y = hand_region.y + hand_region.height * (0.6 - 0.15 * (i-9))
            elif i <= 16:  # Ring finger
                x = hand_region.x + hand_region.width * 0.6
                y = hand_region.y + hand_region.height * (0.6 - 0.15 * (i-13))
            else:  # Pinky
                x = hand_region.x + hand_region.width * 0.7
                y = hand_region.y + hand_region.height * (0.6 - 0.12 * (i-17))
            
            conf = 0.1  # Low confidence for dummy keypoints
            keypoints.extend([x, y, conf])
        
        return np.array(keypoints).reshape(-1, 3)
    
    def get_hand_keypoints(self) -> Tuple[Array, Array]:
        """Get the extracted hand keypoints."""
        return self.hand_keypoints_left, self.hand_keypoints_right
    
    def get_hand_bounding_boxes(self) -> Tuple[Array, Array]:
        """Get the hand bounding boxes."""
        return self.hand_bounding_boxes_left, self.hand_bounding_boxes_right
