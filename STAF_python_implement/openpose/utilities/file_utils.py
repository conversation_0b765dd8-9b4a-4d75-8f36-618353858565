"""
OpenPose File Utilities

This module contains utility functions for file operations.
"""

import json
import os
import shutil
from pathlib import Path
from typing import Dict, Any, List, Optional
import numpy as np

from ..core.array import Array
from ..core.datum import Da<PERSON>


def createDirectory(directory_path: str) -> bool:
    """
    Create directory if it doesn't exist.
    
    Args:
        directory_path: Path to directory
        
    Returns:
        True if successful, False otherwise
    """
    try:
        Path(directory_path).mkdir(parents=True, exist_ok=True)
        return True
    except Exception as e:
        print(f"Error creating directory {directory_path}: {str(e)}")
        return False


def saveKeypointsAsJson(keypoints: Array, output_path: str, 
                       frame_number: int = 0, person_id: int = 0) -> bool:
    """
    Save keypoints as JSON file.
    
    Args:
        keypoints: Keypoints array
        output_path: Output JSON file path
        frame_number: Frame number
        person_id: Person ID
        
    Returns:
        True if successful, False otherwise
    """
    try:
        if keypoints.empty():
            print("Cannot save empty keypoints")
            return False
        
        # Create output directory
        output_dir = Path(output_path).parent
        createDirectory(str(output_dir))
        
        # Convert keypoints to list format
        keypoints_data = keypoints.data
        
        # Handle different array shapes
        if len(keypoints_data.shape) == 3:
            # Multiple people
            people_data = []
            for person_idx in range(keypoints_data.shape[0]):
                person_keypoints = keypoints_data[person_idx].flatten().tolist()
                people_data.append({
                    "person_id": person_idx,
                    "pose_keypoints_2d": person_keypoints
                })
        elif len(keypoints_data.shape) == 2:
            # Single person
            if keypoints_data.shape[1] == 3:
                # Format: (num_parts, 3)
                person_keypoints = keypoints_data.flatten().tolist()
            else:
                # Already flattened
                person_keypoints = keypoints_data[0].tolist()
            
            people_data = [{
                "person_id": person_id,
                "pose_keypoints_2d": person_keypoints
            }]
        else:
            # 1D array
            people_data = [{
                "person_id": person_id,
                "pose_keypoints_2d": keypoints_data.tolist()
            }]
        
        # Create JSON structure
        json_data = {
            "version": 1.3,
            "frame_number": frame_number,
            "people": people_data
        }
        
        # Save to file
        with open(output_path, 'w') as f:
            json.dump(json_data, f, indent=2)
        
        return True
        
    except Exception as e:
        print(f"Error saving keypoints as JSON {output_path}: {str(e)}")
        return False


def loadKeypointsFromJson(json_path: str) -> Optional[Array]:
    """
    Load keypoints from JSON file.
    
    Args:
        json_path: Path to JSON file
        
    Returns:
        Keypoints array or None if failed
    """
    try:
        if not Path(json_path).exists():
            print(f"JSON file not found: {json_path}")
            return None
        
        with open(json_path, 'r') as f:
            json_data = json.load(f)
        
        if "people" not in json_data:
            print("Invalid JSON format: missing 'people' field")
            return None
        
        people_data = json_data["people"]
        if not people_data:
            return Array()
        
        # Convert to numpy array
        all_keypoints = []
        for person in people_data:
            if "pose_keypoints_2d" in person:
                keypoints = person["pose_keypoints_2d"]
                # Reshape to (num_parts, 3)
                num_parts = len(keypoints) // 3
                keypoints_reshaped = np.array(keypoints).reshape(num_parts, 3)
                all_keypoints.append(keypoints_reshaped)
        
        if all_keypoints:
            # Stack all people
            keypoints_array = np.stack(all_keypoints, axis=0)
            return Array(keypoints_array)
        else:
            return Array()
        
    except Exception as e:
        print(f"Error loading keypoints from JSON {json_path}: {str(e)}")
        return None


def saveDatumAsJson(datum: Datum, output_directory: str) -> bool:
    """
    Save datum as JSON files.
    
    Args:
        datum: Datum to save
        output_directory: Output directory
        
    Returns:
        True if successful, False otherwise
    """
    try:
        createDirectory(output_directory)
        
        frame_name = f"{datum.frameNumber:012d}" if datum.frameNumber >= 0 else "000000000000"
        
        # Save pose keypoints
        if datum.poseKeypoints is not None and not datum.poseKeypoints.empty():
            pose_path = os.path.join(output_directory, f"{frame_name}_keypoints.json")
            saveKeypointsAsJson(datum.poseKeypoints, pose_path, datum.frameNumber)
        
        # Save face keypoints
        if datum.faceKeypoints is not None and not datum.faceKeypoints.empty():
            face_path = os.path.join(output_directory, f"{frame_name}_face.json")
            saveKeypointsAsJson(datum.faceKeypoints, face_path, datum.frameNumber)
        
        # Save hand keypoints
        for hand_idx, hand_name in enumerate(["left_hand", "right_hand"]):
            if (datum.handKeypoints[hand_idx] is not None and 
                not datum.handKeypoints[hand_idx].empty()):
                hand_path = os.path.join(output_directory, f"{frame_name}_{hand_name}.json")
                saveKeypointsAsJson(datum.handKeypoints[hand_idx], hand_path, datum.frameNumber)
        
        return True
        
    except Exception as e:
        print(f"Error saving datum as JSON: {str(e)}")
        return False


def getFileList(directory: str, extensions: List[str] = None) -> List[str]:
    """
    Get list of files in directory with specified extensions.
    
    Args:
        directory: Directory path
        extensions: List of file extensions (e.g., ['.jpg', '.png'])
        
    Returns:
        List of file paths
    """
    try:
        if not Path(directory).exists():
            print(f"Directory not found: {directory}")
            return []
        
        if extensions is None:
            extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif']
        
        file_list = []
        for ext in extensions:
            pattern = f"*{ext}"
            files = list(Path(directory).glob(pattern))
            files.extend(list(Path(directory).glob(pattern.upper())))
            file_list.extend([str(f) for f in files])
        
        return sorted(file_list)
        
    except Exception as e:
        print(f"Error getting file list from {directory}: {str(e)}")
        return []


def copyFile(source_path: str, destination_path: str) -> bool:
    """
    Copy file from source to destination.
    
    Args:
        source_path: Source file path
        destination_path: Destination file path
        
    Returns:
        True if successful, False otherwise
    """
    try:
        if not Path(source_path).exists():
            print(f"Source file not found: {source_path}")
            return False
        
        # Create destination directory
        dest_dir = Path(destination_path).parent
        createDirectory(str(dest_dir))
        
        shutil.copy2(source_path, destination_path)
        return True
        
    except Exception as e:
        print(f"Error copying file {source_path} to {destination_path}: {str(e)}")
        return False


def deleteFile(file_path: str) -> bool:
    """
    Delete file.
    
    Args:
        file_path: File path to delete
        
    Returns:
        True if successful, False otherwise
    """
    try:
        if Path(file_path).exists():
            Path(file_path).unlink()
        return True
        
    except Exception as e:
        print(f"Error deleting file {file_path}: {str(e)}")
        return False


def getFileSize(file_path: str) -> int:
    """
    Get file size in bytes.
    
    Args:
        file_path: File path
        
    Returns:
        File size in bytes, -1 if error
    """
    try:
        if Path(file_path).exists():
            return Path(file_path).stat().st_size
        else:
            return -1
            
    except Exception as e:
        print(f"Error getting file size {file_path}: {str(e)}")
        return -1


def ensureDirectoryExists(file_path: str) -> bool:
    """
    Ensure directory exists for the given file path.
    
    Args:
        file_path: File path
        
    Returns:
        True if successful, False otherwise
    """
    try:
        directory = Path(file_path).parent
        return createDirectory(str(directory))
        
    except Exception as e:
        print(f"Error ensuring directory exists for {file_path}: {str(e)}")
        return False


def isValidImageFile(file_path: str) -> bool:
    """
    Check if file is a valid image file.
    
    Args:
        file_path: File path to check
        
    Returns:
        True if valid image file, False otherwise
    """
    try:
        if not Path(file_path).exists():
            return False
        
        valid_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif', '.webp']
        file_ext = Path(file_path).suffix.lower()
        
        return file_ext in valid_extensions
        
    except Exception as e:
        print(f"Error checking image file {file_path}: {str(e)}")
        return False


def isValidVideoFile(file_path: str) -> bool:
    """
    Check if file is a valid video file.
    
    Args:
        file_path: File path to check
        
    Returns:
        True if valid video file, False otherwise
    """
    try:
        if not Path(file_path).exists():
            return False
        
        valid_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm']
        file_ext = Path(file_path).suffix.lower()
        
        return file_ext in valid_extensions
        
    except Exception as e:
        print(f"Error checking video file {file_path}: {str(e)}")
        return False
