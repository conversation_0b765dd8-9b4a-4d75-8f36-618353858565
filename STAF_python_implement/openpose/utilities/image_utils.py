"""
OpenPose Image Utilities

This module contains utility functions for image processing.
"""

import cv2
import numpy as np
from typing import <PERSON><PERSON>, Optional, List
from pathlib import Path

from ..core.array import Array
from ..core.point import Point
from ..core.rectangle import Rectangle


def resizeFixedAspectRatio(image: np.ndarray, target_size: Tuple[int, int], 
                          background_color: Tuple[int, int, int] = (0, 0, 0)) -> <PERSON><PERSON>[np.ndarray, float]:
    """
    Resize image while maintaining aspect ratio.
    
    Args:
        image: Input image
        target_size: Target size (width, height)
        background_color: Background color for padding
        
    Returns:
        Tuple of (resized_image, scale_factor)
    """
    if image is None or image.size == 0:
        return image, 1.0
    
    target_width, target_height = target_size
    original_height, original_width = image.shape[:2]
    
    # Calculate scale factor
    scale_x = target_width / original_width
    scale_y = target_height / original_height
    scale = min(scale_x, scale_y)
    
    # Calculate new dimensions
    new_width = int(original_width * scale)
    new_height = int(original_height * scale)
    
    # Resize image
    resized = cv2.resize(image, (new_width, new_height))
    
    # Create output image with target size
    output = np.full((target_height, target_width, image.shape[2]), background_color, dtype=image.dtype)
    
    # Calculate padding
    pad_x = (target_width - new_width) // 2
    pad_y = (target_height - new_height) // 2
    
    # Place resized image in center
    output[pad_y:pad_y + new_height, pad_x:pad_x + new_width] = resized
    
    return output, scale


def putTextOnImage(image: np.ndarray, text: str, position: Tuple[int, int],
                  font_scale: float = 0.7, color: Tuple[int, int, int] = (255, 255, 255),
                  thickness: int = 2, background_color: Optional[Tuple[int, int, int]] = None) -> np.ndarray:
    """
    Put text on image with optional background.
    
    Args:
        image: Input image
        text: Text to draw
        position: Text position (x, y)
        font_scale: Font scale
        color: Text color
        thickness: Text thickness
        background_color: Background color (optional)
        
    Returns:
        Image with text
    """
    output = image.copy()
    font = cv2.FONT_HERSHEY_SIMPLEX
    
    # Get text size
    (text_width, text_height), baseline = cv2.getTextSize(text, font, font_scale, thickness)
    
    x, y = position
    
    # Draw background rectangle if specified
    if background_color is not None:
        cv2.rectangle(output, 
                     (x - 5, y - text_height - 5),
                     (x + text_width + 5, y + baseline + 5),
                     background_color, -1)
    
    # Draw text
    cv2.putText(output, text, (x, y), font, font_scale, color, thickness)
    
    return output


def drawKeypoints(image: np.ndarray, keypoints: Array, 
                 connections: Optional[List[Tuple[int, int]]] = None,
                 colors: Optional[List[Tuple[int, int, int]]] = None,
                 point_radius: int = 4, line_thickness: int = 2,
                 confidence_threshold: float = 0.1) -> np.ndarray:
    """
    Draw keypoints and connections on image.
    
    Args:
        image: Input image
        keypoints: Keypoints array
        connections: List of connections between keypoints
        colors: Colors for each keypoint/connection
        point_radius: Radius of keypoint circles
        line_thickness: Thickness of connection lines
        confidence_threshold: Minimum confidence to draw keypoint
        
    Returns:
        Image with drawn keypoints
    """
    if image is None or keypoints.empty():
        return image
    
    output = image.copy()
    keypoints_data = keypoints.data
    
    # Handle different array shapes
    if len(keypoints_data.shape) == 3:
        # Multiple people
        for person_id in range(keypoints_data.shape[0]):
            person_keypoints = keypoints_data[person_id]
            output = _draw_single_person_keypoints(
                output, person_keypoints, connections, colors,
                point_radius, line_thickness, confidence_threshold
            )
    elif len(keypoints_data.shape) == 2:
        # Single person
        output = _draw_single_person_keypoints(
            output, keypoints_data, connections, colors,
            point_radius, line_thickness, confidence_threshold
        )
    
    return output


def _draw_single_person_keypoints(image: np.ndarray, keypoints: np.ndarray,
                                connections: Optional[List[Tuple[int, int]]],
                                colors: Optional[List[Tuple[int, int, int]]],
                                point_radius: int, line_thickness: int,
                                confidence_threshold: float) -> np.ndarray:
    """Draw keypoints for a single person."""
    output = image.copy()
    
    # Default colors
    if colors is None:
        colors = [(0, 255, 0)] * len(keypoints)  # Green for all points
    
    # Draw connections first (so they appear behind points)
    if connections is not None:
        for i, (start_idx, end_idx) in enumerate(connections):
            if (start_idx < len(keypoints) and end_idx < len(keypoints)):
                start_kpt = keypoints[start_idx]
                end_kpt = keypoints[end_idx]
                
                if (len(start_kpt) >= 3 and len(end_kpt) >= 3 and
                    start_kpt[2] > confidence_threshold and end_kpt[2] > confidence_threshold):
                    
                    start_point = (int(start_kpt[0]), int(start_kpt[1]))
                    end_point = (int(end_kpt[0]), int(end_kpt[1]))
                    
                    color = colors[i % len(colors)]
                    cv2.line(output, start_point, end_point, color, line_thickness)
    
    # Draw keypoints
    for i, kpt in enumerate(keypoints):
        if len(kpt) >= 3 and kpt[2] > confidence_threshold:
            center = (int(kpt[0]), int(kpt[1]))
            color = colors[i % len(colors)]
            cv2.circle(output, center, point_radius, color, -1)
    
    return output


def cropImageAroundKeypoints(image: np.ndarray, keypoints: Array,
                           margin: float = 0.2, min_size: int = 64) -> Tuple[np.ndarray, Rectangle]:
    """
    Crop image around keypoints with margin.
    
    Args:
        image: Input image
        keypoints: Keypoints array
        margin: Margin around keypoints (as fraction of bounding box size)
        min_size: Minimum crop size
        
    Returns:
        Tuple of (cropped_image, crop_rectangle)
    """
    if image is None or keypoints.empty():
        return image, Rectangle()
    
    from .keypoint import getKeypointsRectangle
    
    # Get bounding rectangle of keypoints
    bbox = getKeypointsRectangle(keypoints)
    
    if bbox.width == 0 or bbox.height == 0:
        return image, Rectangle()
    
    # Add margin
    margin_x = bbox.width * margin
    margin_y = bbox.height * margin
    
    crop_x = max(0, int(bbox.x - margin_x))
    crop_y = max(0, int(bbox.y - margin_y))
    crop_width = min(image.shape[1] - crop_x, int(bbox.width + 2 * margin_x))
    crop_height = min(image.shape[0] - crop_y, int(bbox.height + 2 * margin_y))
    
    # Ensure minimum size
    if crop_width < min_size:
        center_x = crop_x + crop_width // 2
        crop_x = max(0, center_x - min_size // 2)
        crop_width = min(min_size, image.shape[1] - crop_x)
    
    if crop_height < min_size:
        center_y = crop_y + crop_height // 2
        crop_y = max(0, center_y - min_size // 2)
        crop_height = min(min_size, image.shape[0] - crop_y)
    
    # Crop image
    cropped = image[crop_y:crop_y + crop_height, crop_x:crop_x + crop_width]
    crop_rect = Rectangle(crop_x, crop_y, crop_width, crop_height)
    
    return cropped, crop_rect


def overlayHeatmap(image: np.ndarray, heatmap: np.ndarray, 
                  alpha: float = 0.7, colormap: int = cv2.COLORMAP_JET) -> np.ndarray:
    """
    Overlay heatmap on image.
    
    Args:
        image: Base image
        heatmap: Heatmap to overlay
        alpha: Alpha blending factor
        colormap: OpenCV colormap
        
    Returns:
        Image with heatmap overlay
    """
    if image is None or heatmap is None:
        return image
    
    # Resize heatmap to match image size
    if heatmap.shape[:2] != image.shape[:2]:
        heatmap_resized = cv2.resize(heatmap, (image.shape[1], image.shape[0]))
    else:
        heatmap_resized = heatmap
    
    # Normalize heatmap
    heatmap_norm = cv2.normalize(heatmap_resized, None, 0, 255, cv2.NORM_MINMAX, cv2.CV_8U)
    
    # Apply colormap
    heatmap_colored = cv2.applyColorMap(heatmap_norm, colormap)
    
    # Blend with original image
    output = cv2.addWeighted(image, 1.0 - alpha, heatmap_colored, alpha, 0)
    
    return output


def loadImage(image_path: str) -> Optional[np.ndarray]:
    """
    Load image from file.
    
    Args:
        image_path: Path to image file
        
    Returns:
        Loaded image or None if failed
    """
    try:
        if not Path(image_path).exists():
            print(f"Image file not found: {image_path}")
            return None
        
        image = cv2.imread(image_path)
        
        if image is None:
            print(f"Failed to load image: {image_path}")
            return None
        
        return image
        
    except Exception as e:
        print(f"Error loading image {image_path}: {str(e)}")
        return None


def saveImage(image: np.ndarray, output_path: str) -> bool:
    """
    Save image to file.
    
    Args:
        image: Image to save
        output_path: Output file path
        
    Returns:
        True if successful, False otherwise
    """
    try:
        if image is None:
            print("Cannot save None image")
            return False
        
        # Create output directory if it doesn't exist
        output_dir = Path(output_path).parent
        output_dir.mkdir(parents=True, exist_ok=True)
        
        success = cv2.imwrite(output_path, image)
        
        if not success:
            print(f"Failed to save image: {output_path}")
            return False
        
        return True
        
    except Exception as e:
        print(f"Error saving image {output_path}: {str(e)}")
        return False


def convertColorSpace(image: np.ndarray, conversion: int) -> np.ndarray:
    """
    Convert image color space.
    
    Args:
        image: Input image
        conversion: OpenCV color conversion code
        
    Returns:
        Converted image
    """
    if image is None:
        return image
    
    try:
        return cv2.cvtColor(image, conversion)
    except Exception as e:
        print(f"Error converting color space: {str(e)}")
        return image
