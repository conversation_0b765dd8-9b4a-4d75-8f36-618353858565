"""
OpenPose Python Implementation

This module provides a pure Python implementation of OpenPose for real-time 
multi-person 2D pose estimation using Part Affinity Fields.

Main classes:
    - WrapperPython: Main wrapper for OpenPose functionality
    - Datum: Data container for input/output
    - Array: Multi-dimensional array container
    - Point: 2D/3D point representation
    - Rectangle: Rectangle representation
"""

from .core import *
from .pose import *
from .face import *
from .hand import *
from .net import *
from .utilities import *
from .wrapper import *

# Version information
__version__ = "1.7.0"
__author__ = "OpenPose Python Implementation"

# Main exports
__all__ = [
    # Core classes
    'Array', 'Point', 'Rectangle', 'Matrix', 'Datum',
    
    # Wrapper classes
    'WrapperPython', 'VectorDatum',
    
    # Pose estimation
    'PoseExtractor', 'PoseRenderer', 'PoseModel',
    
    # Face detection
    'FaceExtractor', 'FaceRenderer', 'FaceDetector',
    
    # Hand detection
    'HandExtractor', 'HandRenderer', 'HandDetector',
    
    # Utilities
    'flagsToRenderMode', 'stringToDataFormat',
    
    # Enums
    'ThreadManagerMode', 'RenderMode', 'DataFormat', 'ScaleMode',
    'HeatMapType', 'PoseModel', 'DetectionMode'
]
