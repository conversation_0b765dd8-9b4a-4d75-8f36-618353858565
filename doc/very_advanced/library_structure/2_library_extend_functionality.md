OpenPose Very Advanced Doc - Library Structure - How to Extend Functionality
====================================

If you intend to extend the functionality of our library:

1. Read the [README.md](../../../README.md) page.
2. Check the basic library overview doc on [doc/very_advanced/library_structure/1_library_deep_overview.md](1_library_deep_overview.md).
3. Read, understand and play with the basic real time pose demo source code [OpenPose demo](../../doc/01_demo.md) and [C++ API](../../doc/04_cpp_api.md). It includes all the functionality of our library, and it has been properly commented.
4. Read, understand and play with the other tutorials in [examples/](https://github.com/CMU-Perceptual-Computing-Lab/openpose/tree/master/examples). It includes more specific examples.
5. Check the basic UML diagram on the [doc/very_advanced/library_structure/UML](UML/) to get an idea of each module relations.
6. Take a look to the stucuture of the already existing modules.
7. The C++ headers files add documentation in [Doxygen](http://www.doxygen.org/) format. Create this documentation by compiling the [include](https://github.com/CMU-Perceptual-Computing-Lab/openpose/tree/master/include) folder with Doxygen. This documentation is slowly but continuously improved.
8. You can also take a look to the source code or ask us on GitHub.
