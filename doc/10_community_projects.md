OpenPose Doc - Community-based Projects
====================================

Here we expose all projects created with OpenPose by the community and that were shared with us. Do you want to share yours? Simply create a pull request and add to this file your demo and a description of it!

1. [**ROS OpenPose**](https://github.com/ildoonet/ros-openpose)
2. [**Hand gesture classification application - OpenHand**](https://github.com/ArthurFDLR/OpenHand-App): Third-party application that eases hand keypoints datasets creation and real-time hand gesture classification. You can deploy your own Neural Network classification model on top of OpenPose and play with it in real-time through a GUI!
3. [Add here your demo name and link](#here_some_full_link): Add here the description of your project.

Disclaimer: We do not support any of these projects, we are simply exposing them. GitHub issues or questions about those will result in strict user bans and the posts being deleted.
