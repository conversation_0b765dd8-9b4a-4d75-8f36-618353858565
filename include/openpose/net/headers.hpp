#ifndef OPENPOSE_NET_HEADERS_HPP
#define OPENPOSE_NET_HEADERS_HPP

// net module
#include <openpose/net/bodyPartConnectorBase.hpp>
#include <openpose/net/bodyPartConnectorCaffe.hpp>
#include <openpose/net/maximumBase.hpp>
#include <openpose/net/maximumCaffe.hpp>
#include <openpose/net/net.hpp>
#include <openpose/net/netCaffe.hpp>
#include <openpose/net/netOpenCv.hpp>
#include <openpose/net/nmsBase.hpp>
#include <openpose/net/nmsCaffe.hpp>
#include <openpose/net/resizeAndMergeBase.hpp>
#include <openpose/net/resizeAndMergeCaffe.hpp>

#endif // OPENPOSE_NET_HEADERS_HPP
