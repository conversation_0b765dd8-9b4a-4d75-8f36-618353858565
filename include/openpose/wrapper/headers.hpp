#ifndef OPENPOSE_WRAPPER_HEADERS_HPP
#define OPENPOSE_WRAPPER_HEADERS_HPP

// wrapper module
#include <openpose/wrapper/enumClasses.hpp>
#include <openpose/wrapper/wrapper.hpp>
#include <openpose/wrapper/wrapperAuxiliary.hpp>
#include <openpose/wrapper/wrapperStructFace.hpp>
#include <openpose/wrapper/wrapperStructGui.hpp>
#include <openpose/wrapper/wrapperStructHand.hpp>
#include <openpose/wrapper/wrapperStructInput.hpp>
#include <openpose/wrapper/wrapperStructOutput.hpp>
#include <openpose/wrapper/wrapperStructPose.hpp>

#endif // OPENPOSE_WRAPPER_HEADERS_HPP
