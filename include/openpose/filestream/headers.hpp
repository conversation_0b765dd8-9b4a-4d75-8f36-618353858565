#ifndef OPENPOSE_FILESTREAM_HEADERS_HPP
#define OPENPOSE_FILESTREAM_HEADERS_HPP

// fileStream module
#include <openpose/filestream/bvhSaver.hpp>
#include <openpose/filestream/cocoJsonSaver.hpp>
#include <openpose/filestream/enumClasses.hpp>
#include <openpose/filestream/fileSaver.hpp>
#include <openpose/filestream/fileStream.hpp>
#include <openpose/filestream/heatMapSaver.hpp>
#include <openpose/filestream/imageSaver.hpp>
#include <openpose/filestream/jsonOfstream.hpp>
#include <openpose/filestream/keypointSaver.hpp>
#include <openpose/filestream/peopleJsonSaver.hpp>
#include <openpose/filestream/udpSender.hpp>
#include <openpose/filestream/videoSaver.hpp>
#include <openpose/filestream/wBvhSaver.hpp>
#include <openpose/filestream/wCocoJsonSaver.hpp>
#include <openpose/filestream/wFaceSaver.hpp>
#include <openpose/filestream/wHandSaver.hpp>
#include <openpose/filestream/wImageSaver.hpp>
#include <openpose/filestream/wHeatMapSaver.hpp>
#include <openpose/filestream/wPeopleJsonSaver.hpp>
#include <openpose/filestream/wPoseSaver.hpp>
#include <openpose/filestream/wUdpSender.hpp>
#include <openpose/filestream/wVideoSaver.hpp>
#include <openpose/filestream/wVideoSaver3D.hpp>

#endif // OPENPOSE_FILESTREAM_HEADERS_HPP
