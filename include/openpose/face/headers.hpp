#ifndef OPENPOSE_FACE_HEADERS_HPP
#define OPENPOSE_FACE_HEADERS_HPP

// face module
#include <openpose/face/faceDetector.hpp>
#include <openpose/face/faceDetectorOpenCV.hpp>
#include <openpose/face/faceExtractorCaffe.hpp>
#include <openpose/face/faceExtractorNet.hpp>
#include <openpose/face/faceParameters.hpp>
#include <openpose/face/faceCpuRenderer.hpp>
#include <openpose/face/faceGpuRenderer.hpp>
#include <openpose/face/faceRenderer.hpp>
#include <openpose/face/renderFace.hpp>
#include <openpose/face/wFaceDetector.hpp>
#include <openpose/face/wFaceDetectorOpenCV.hpp>
#include <openpose/face/wFaceExtractorNet.hpp>
#include <openpose/face/wFaceRenderer.hpp>

#endif // OPENPOSE_FACE_HEADERS_HPP
